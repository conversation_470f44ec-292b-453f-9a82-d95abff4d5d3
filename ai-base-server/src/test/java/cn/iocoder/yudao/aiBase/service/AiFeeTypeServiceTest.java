package cn.iocoder.yudao.aiBase.service;

import cn.iocoder.yudao.aiBase.config.BaseConstant;
import cn.iocoder.yudao.aiBase.entity.AiFeeType;
import cn.iocoder.yudao.aiBase.service.impl.AiFeeTypeServiceImpl;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import jakarta.annotation.Resource;
import java.math.BigDecimal;
import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * AI费用类型服务测试类
 * 
 * <AUTHOR>
 */
@SpringBootTest
@ActiveProfiles("test")
public class AiFeeTypeServiceTest {

    @Resource
    private AiFeeTypeService aiFeeTypeService;

    @Test
    public void testOnlineOfflineFeeType() {
        // 创建测试数据
        AiFeeType testFeeType = createTestFeeType();
        Integer id = aiFeeTypeService.createFeeType(convertToSaveReqVO(testFeeType));
        
        // 测试上架
        aiFeeTypeService.onlineFeeType(id);
        AiFeeType onlineFeeType = aiFeeTypeService.getFeeType(id);
        assertEquals(BaseConstant.ONE, onlineFeeType.getOnline());
        
        // 测试下架
        aiFeeTypeService.offlineFeeType(id);
        AiFeeType offlineFeeType = aiFeeTypeService.getFeeType(id);
        assertEquals(BaseConstant.ZERO, offlineFeeType.getOnline());
        
        // 清理测试数据
        aiFeeTypeService.deleteFeeType(id);
    }

    @Test
    public void testBatchOnlineOfflineFeeType() {
        // 创建多个测试数据
        AiFeeType testFeeType1 = createTestFeeType();
        testFeeType1.setDisplayName("测试费用类型1");
        Integer id1 = aiFeeTypeService.createFeeType(convertToSaveReqVO(testFeeType1));
        
        AiFeeType testFeeType2 = createTestFeeType();
        testFeeType2.setDisplayName("测试费用类型2");
        Integer id2 = aiFeeTypeService.createFeeType(convertToSaveReqVO(testFeeType2));
        
        List<Integer> ids = Arrays.asList(id1, id2);
        
        // 测试批量上架
        aiFeeTypeService.batchOnlineFeeType(ids);
        AiFeeType feeType1 = aiFeeTypeService.getFeeType(id1);
        AiFeeType feeType2 = aiFeeTypeService.getFeeType(id2);
        assertEquals(BaseConstant.ONE, feeType1.getOnline());
        assertEquals(BaseConstant.ONE, feeType2.getOnline());
        
        // 测试批量下架
        aiFeeTypeService.batchOfflineFeeType(ids);
        feeType1 = aiFeeTypeService.getFeeType(id1);
        feeType2 = aiFeeTypeService.getFeeType(id2);
        assertEquals(BaseConstant.ZERO, feeType1.getOnline());
        assertEquals(BaseConstant.ZERO, feeType2.getOnline());
        
        // 清理测试数据
        aiFeeTypeService.deleteFeeType(id1);
        aiFeeTypeService.deleteFeeType(id2);
    }

    @Test
    public void testCopyFeeType() {
        // 创建原始测试数据
        AiFeeType originalFeeType = createTestFeeType();
        originalFeeType.setDisplayName("原始费用类型");
        originalFeeType.setOnline(BaseConstant.ONE); // 设置为上架状态
        Integer originalId = aiFeeTypeService.createFeeType(convertToSaveReqVO(originalFeeType));
        
        // 测试复制
        Integer copyId = aiFeeTypeService.copyFeeType(originalId);
        assertNotNull(copyId);
        assertNotEquals(originalId, copyId);
        
        // 验证复制的数据
        AiFeeType copyFeeType = aiFeeTypeService.getFeeType(copyId);
        assertNotNull(copyFeeType);
        assertEquals(BaseConstant.ZERO, copyFeeType.getOnline()); // 复制后应该是下架状态
        assertTrue(copyFeeType.getDisplayName().contains("_副本"));
        assertEquals(originalFeeType.getAppUuid(), copyFeeType.getAppUuid());
        assertEquals(originalFeeType.getFeePrice(), copyFeeType.getFeePrice());
        
        // 清理测试数据
        aiFeeTypeService.deleteFeeType(originalId);
        aiFeeTypeService.deleteFeeType(copyId);
    }

    private AiFeeType createTestFeeType() {
        return AiFeeType.builder()
                .appUuid("test-app-uuid")
                .lang("zh")
                .displayName("测试费用类型")
                .packageKey("test-package")
                .packageType("测试订阅")
                .feePrice(new BigDecimal("29.90"))
                .online(BaseConstant.ZERO)
                .build();
    }

    private cn.iocoder.yudao.aiBase.dto.request.AiFeeTypeSaveReqVO convertToSaveReqVO(AiFeeType feeType) {
        cn.iocoder.yudao.aiBase.dto.request.AiFeeTypeSaveReqVO reqVO = 
            new cn.iocoder.yudao.aiBase.dto.request.AiFeeTypeSaveReqVO();
        reqVO.setAppUuid(feeType.getAppUuid());
        reqVO.setLang(feeType.getLang());
        reqVO.setDisplayName(feeType.getDisplayName());
        reqVO.setPackageKey(feeType.getPackageKey());
        reqVO.setPackageType(feeType.getPackageType());
        reqVO.setFeePrice(feeType.getFeePrice());
        reqVO.setOnline(feeType.getOnline());
        return reqVO;
    }
}
