package cn.iocoder.yudao.aiBase.dto.alipay;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

@Data
public class GenerateMergeCodeRequest {
    @Schema(description = "业务系统的订单id", required = true)
    @NotBlank(message = "appOrderId不可为空")
    private String appOrderId;

    @Schema(description = "接入应用的appId", required = true)
    @NotBlank(message = "accessAppId不可为空")
    private String accessAppId;
}
