package cn.iocoder.yudao.aiBase.dto.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.math.BigDecimal;

@Schema(description = "管理后台 - AI费用类型新增/修改 Request VO")
@Data
public class AiFeeTypeSaveReqVO {

    @Schema(description = "主键", example = "1")
    private Integer id;

    @Schema(description = "应用uuid", requiredMode = Schema.RequiredMode.REQUIRED, example = "app-uuid-123")
    @NotBlank(message = "应用uuid不能为空")
    private String appUuid;

    @Schema(description = "应用语言", example = "zh")
    private String lang;

    @Schema(description = "显示名称，一般是订阅方式", example = "连续包月")
    private String displayName;

    @Schema(description = "订阅套餐，一般是appNameEn", example = "ai-writer")
    @NotBlank(message = "订阅套餐不能为空")
    private String packageKey;

    @Schema(description = "订阅方式", requiredMode = Schema.RequiredMode.REQUIRED, example = "连续包月")
    @NotBlank(message = "订阅方式不能为空")
    private String packageType;

    @Schema(description = "周期类型，默认月", example = "MONTH")
    private String periodType = "MONTH";

    @Schema(description = "时长", example = "1")
    private Integer monthNum = 1;

    @Schema(description = "原价格", example = "99.00")
    private BigDecimal oldPrice;

    @Schema(description = "价格", requiredMode = Schema.RequiredMode.REQUIRED, example = "29.90")
    @NotNull(message = "价格不能为空")
    private BigDecimal feePrice;

    @Schema(description = "币种", example = "人民币")
    private String coinType = "人民币";

    @Schema(description = "价格ID，币种是人民币的是支付宝场景值，美元的是stripe", example = "price_123")
    private String priceId;

    @Schema(description = "可使用次数，-1不限制", example = "100")
    private Integer num;

    @Schema(description = "1开启，0下架", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "在线状态不能为空")
    private Integer online;

    @Schema(description = "支付宝订阅，stripe订阅，按次收费", example = "支付宝订阅")
    private String priceType;

    @Schema(description = "购买后的有效期", example = "12")
    private Integer expiredMonths;

    @Schema(description = "排序，越小越前", example = "1")
    private Integer orderNum;

    @Schema(description = "备注", example = "这是一个测试费用类型")
    private String remark;

}
