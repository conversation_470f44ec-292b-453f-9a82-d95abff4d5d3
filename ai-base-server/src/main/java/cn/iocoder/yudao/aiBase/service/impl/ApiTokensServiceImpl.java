package cn.iocoder.yudao.aiBase.service.impl;

import cn.iocoder.yudao.aiBase.config.BaseConstant;
import cn.iocoder.yudao.aiBase.config.DBConstant;
import cn.iocoder.yudao.aiBase.config.ErrorCodeConstants;
import cn.iocoder.yudao.aiBase.config.SseConfig;
import cn.iocoder.yudao.aiBase.dto.param.ApiTokensParam;
import cn.iocoder.yudao.aiBase.dto.request.dify.*;
import cn.iocoder.yudao.aiBase.dto.response.AppBaseResponse;
import cn.iocoder.yudao.aiBase.dto.response.DifyBaseResponse;
import cn.iocoder.yudao.aiBase.dto.response.MyCollectionResponse;
import cn.iocoder.yudao.aiBase.dto.response.dify.UserSessionResponse;
import cn.iocoder.yudao.aiBase.entity.ApiTokens;
import cn.iocoder.yudao.aiBase.mapper.ApiTokensMapper;
import cn.iocoder.yudao.aiBase.service.*;
import cn.iocoder.yudao.aiBase.util.CommonUtil;
import cn.iocoder.yudao.aiBase.util.DifyUtil;
import cn.iocoder.yudao.framework.common.exception.ErrorCode;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.codec.ServerSentEvent;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;
import reactor.core.publisher.Flux;
import reactor.core.scheduler.Schedulers;

import java.util.*;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;

@Slf4j
@Service
@DS(DBConstant.DifyBase)
public class ApiTokensServiceImpl extends ServiceImpl<ApiTokensMapper, ApiTokens> implements ApiTokensService {

    private Map<String, String> apiKeyMap = new HashMap<>();

    @Value("${dify-base.host}")
    private String host;

    @Value("${spring.profiles.active}")
    private String active;

    @Autowired
    private SseConfig sseConfig;

    @Override
    public LambdaQueryWrapper getLambda(ApiTokensParam param) {
        LambdaQueryWrapper<ApiTokens> lambda = new LambdaQueryWrapper<>();
        if (param == null) {
            return lambda;
        }
        UUID appId = StringUtils.isEmpty(param.getAppId()) ? null : UUID.fromString(param.getAppId());
        lambda
                .eq(appId!=null, ApiTokens::getAppId, appId)
                .eq(StringUtils.isNotEmpty(param.getToken()), ApiTokens::getToken, param.getToken())
        ;

        return lambda;
    }

    @Override
    public void refreshToken() {
        int i = apiKeyMap.size();
        apiKeyMap.clear();
        getList(null).forEach(item -> {
            apiKeyMap.put(item.getAppId(), item.getToken());
        });
        i = apiKeyMap.size();
        log.info("refreshToken:{}", i);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public String getToken(String appId) {
        String token = apiKeyMap.get(appId);
        if (token == null) {
            getList(ApiTokensParam.builder().appId(appId).build()).forEach(item -> {
                apiKeyMap.put(item.getAppId(), item.getToken());
            });
            token = apiKeyMap.get(appId);
        }
        log.info("获取token: {} - {}" , appId, token);
        if (token == null) {
            throw exception(ErrorCodeConstants.ERROR_5036);
        }
        return token;
    }

    @Override
    public Flux<ServerSentEvent> chatMsg(ChatMessagesRequest param) {
        try {
            String p = JSON.toJSONString(param);
            JSONObject pJson = JSONObject.parseObject(p);
            pJson.put("conversation_id", param.getConversation_id());
            String token = getToken(param.getAppId());
            return DifyUtil.getFlux(host, DifyUtil.ChatMsg, pJson, token, p.contains("upload_file_id"));
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    @Override
    public JSONObject chatMsg1(ChatMessagesRequest param) {
        String token = getToken(param.getAppId());
        param.setResponse_mode("blocking");
        JSONObject res = DifyUtil.chatMsg(token, param, host, !BaseConstant.PROD_STR.equals(active));
        if (res == null) {
            throw exception(ErrorCodeConstants.ERROR_5038);
        }
        return res;
    }

    @Override
    public JSONObject stopChat(StopChatRequest param) {
        String token = getToken(param.getAppId());
        JSONObject res = DifyUtil.stopChat(token, param, host, !BaseConstant.PROD_STR.equals(active));
        if (res == null) {
            throw exception(ErrorCodeConstants.ERROR_5038);
        }
        return res;
    }

    @Override
    public JSONObject feedback(FeedbackRequest param) {
        String token = getToken(param.getAppId());
        JSONObject res = DifyUtil.feedback(token, param, host, !BaseConstant.PROD_STR.equals(active));
        if (res == null) {
            throw exception(ErrorCodeConstants.ERROR_5038);
        }
        return res;
    }

    @Override
    public JSONObject suggested(SuggestedRequest param) {
        String token = getToken(param.getAppId());
        JSONObject res = DifyUtil.suggested(token, param, host, !BaseConstant.PROD_STR.equals(active));
        if (res == null) {
            throw exception(ErrorCodeConstants.ERROR_5038);
        }
        return res;
    }

    @Override
    public JSONObject messages(MessagesRequest param) {
        String token = getToken(param.getAppId());
        JSONObject res = DifyUtil.messages(token, param, host, !BaseConstant.PROD_STR.equals(active));
        if (res == null) {
            throw exception(ErrorCodeConstants.ERROR_5038);
        }
        return res;
    }

    @Override
    public JSONObject conversations(ConversationsRequest param) {
        String token = getToken(param.getAppId());
        JSONObject res = DifyUtil.conversations(token, param, host, !BaseConstant.PROD_STR.equals(active));
        if (res == null) {
            throw exception(ErrorCodeConstants.ERROR_5038);
        }
        return res;
    }

    @Override
    public Boolean deleteConverse(DeleteConversationsRequest param) {
        String token = getToken(param.getAppId());
        JSONObject res = DifyUtil.deleteConverse(token, param, host, !BaseConstant.PROD_STR.equals(active));

        return true;
    }

    @Override
    public JSONObject renameConverse(RenameRequest param) {
        String token = getToken(param.getAppId());
        JSONObject res = DifyUtil.renameConverse(token, param, host, !BaseConstant.PROD_STR.equals(active));
        if (res == null) {
            throw exception(ErrorCodeConstants.ERROR_5038);
        }
        return res;
    }

    @Override
    public JSONObject parameters(DifyBaseRequest param) {
        String token = getToken(param.getAppId());
        JSONObject res = DifyUtil.parameters(token, param, host, !BaseConstant.PROD_STR.equals(active));
        if (res == null) {
            throw exception(ErrorCodeConstants.ERROR_5038);
        }
        return res;
    }

    @Override
    public DifyBaseResponse getAppPrePrompt(String appId) {
        DifyBaseResponse res = baseMapper.getAppPrePrompt(appId);
        res.setIconUrl(getIconUrl(res.getIconUrl()));
        return res;
    }

    @Override
    public List<DifyBaseResponse> getAppsPrePrompt(List<String> appIds) {
        if (appIds==null || appIds.isEmpty()) {
            return new ArrayList<>();
        }
        List<DifyBaseResponse> res = baseMapper.getAppsPrePrompt(appIds);
        res.forEach(item -> {
            item.setIconUrl(getIconUrl(item.getIconUrl()));
        });
        return res;
    }

    @Override
    public List<DifyBaseResponse> getAppsPrePrompt(String appIds) {
        if (appIds==null || appIds.isEmpty()) {
            return new ArrayList<>();
        }
        List<DifyBaseResponse> res = getAppsPrePrompt(Arrays.asList(appIds.split(",")));
        res.forEach(item -> {
            item.setIconUrl(getIconUrl(item.getIconUrl()));
        });
        return res;
    }

    @Override
    public List<MyCollectionResponse>myCollection(MyCollectionRequest param) {
        if (param.getAppIds()==null || param.getAppIds().isEmpty()) {
            return new ArrayList<>();
        }

        return baseMapper.myCollection(param.getAppIds(), param.getUser(), param.getStatus(), param.getPageSize(),
            (param.getPageIndex()-1)*param.getPageSize());
    }

    @Override
    public Flux<ServerSentEvent> workflowsRun(WorkflowsRunRequest param) {
        try {
            String p = JSON.toJSONString(param);
            JSONObject pJson = JSONObject.parseObject(p);
            String token = getToken(param.getAppId());
            return DifyUtil.getFlux(host, DifyUtil.WorkflowsRun, pJson, token, p.contains("upload_file_id"));
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    @Override
    public JSONObject workflowsRun1(WorkflowsRunRequest param) {
        String token = getToken(param.getAppId());
        JSONObject res = DifyUtil.workflowsRun(token, param, host, !BaseConstant.PROD_STR.equals(active));
        if (res == null) {
            throw exception(ErrorCodeConstants.ERROR_5038);
        }
        return res;
    }

    @Override
    public JSONObject meta(DifyBaseRequest param) {
        String token = getToken(param.getAppId());
        JSONObject res = DifyUtil.meta(token, param, host, !BaseConstant.PROD_STR.equals(active));
        if (res == null) {
            throw exception(ErrorCodeConstants.ERROR_5038);
        }
        return res;
    }

    @Override
    public List<AppBaseResponse> getAppsByTag(String tag) {
        if (tag==null || tag.isEmpty()) {
            return new ArrayList<>();
        }
        List<AppBaseResponse> res = baseMapper.getAppsByTag(tag);
        res.forEach(item -> {
            item.setIconUrl(getIconUrl(item.getIconUrl()));
        });
        return res;
    }

    @Override
    public List<DifyBaseResponse> getAppsByName(String name) {
        if (name==null || name.isEmpty()) {
            return new ArrayList<>();
        }
        List<DifyBaseResponse> res = baseMapper.getAppsByName(name);
        res.forEach(item -> {
            item.setIconUrl(getIconUrl(item.getIconUrl()));
        });
        return res;
    }

    private String getIconUrl(String iconUrl) {
        if (iconUrl==null || iconUrl.length()<4) {
            return BaseConstant.EMPTY_STR;
        }
        return "/files/public/"+iconUrl+"/image-preview";
    }

    @Override
    public Flux<ServerSentEvent> completionMsg(WorkflowsRunRequest param) {
        try {
            String p = JSON.toJSONString(param);
            JSONObject pJson = JSONObject.parseObject(p);
            String token = getToken(param.getAppId());
            return DifyUtil.getFlux(host, DifyUtil.CompletionMsg, pJson, token, p.contains("upload_file_id"));
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    @Override
    public JSONObject completionMsg1(WorkflowsRunRequest param) {
        String token = getToken(param.getAppId());
        JSONObject res = DifyUtil.completionMsg(token, param, host, !BaseConstant.PROD_STR.equals(active));
        if (res == null) {
            throw exception(ErrorCodeConstants.ERROR_5038);
        }
        return res;
    }

    @Override
    public JSONObject filesUpload(FilesUploadRequest uploadReqVO) {
        String token = getToken(uploadReqVO.getAppId());
        JSONObject res = DifyUtil.filesUpload(token, uploadReqVO, host, !BaseConstant.PROD_STR.equals(active));
        res.put("type", CommonUtil.getFileType(res.getString("name")));
        res.put("transfer_method", "local_file");
        res.put("upload_file_id", res.getString("id"));
        res.put("preview_url", getSignedFileUrl(res.getString("id")));
        return res;
    }

    @Override
    public SseEmitter getEmitter(Flux<ServerSentEvent> flux) {
        if (flux == null) {
            return getEmitter(null, ErrorCodeConstants.ERROR_5039);
        }
        SseEmitter emitter = new SseEmitter(sseConfig.getTimeout()); // 使用配置化的超时时间

        // 设置超时回调
        emitter.onTimeout(() -> {
            log.warn("SSE connection timeout after {} ms", sseConfig.getTimeout());
            try {
                JSONObject timeoutMsg = new JSONObject();
                timeoutMsg.put("event", "timeout");
                timeoutMsg.put("message", "连接超时，请重新发起请求");
                timeoutMsg.put("code", ErrorCodeConstants.ERROR_5039.getCode());
                timeoutMsg.put("timestamp", System.currentTimeMillis());
                emitter.send(SseEmitter.event().name("timeout").data(timeoutMsg.toJSONString()));
            } catch (Exception e) {
                log.error("Failed to send timeout message", e);
            }
        });

        // 设置完成回调
        emitter.onCompletion(() -> {
            log.info("SSE connection completed successfully");
        });

        // 设置错误回调
        emitter.onError((e) -> {
            if (isClientDisconnectedException(e)) {
                if (sseConfig.isEnableClientDisconnectDebugLog()) {
                    log.debug("SSE connection closed by client: {}", e.getMessage());
                }
            } else {
                log.error("SSE connection error", e);
            }
        });

        // 使用subscribeOn来确保在IO线程池中处理，避免阻塞
        flux.subscribeOn(Schedulers.boundedElastic())
            // 使用publishOn确保发送操作在合适的线程中执行
            .publishOn(Schedulers.boundedElastic())
            .doOnNext(data -> sendData(emitter, data))
            .doOnError(e -> handleError(emitter, e))
            .doOnComplete(() -> completeEmitter(emitter))
            // 使用subscribe的重载方法来更好地处理错误
            .subscribe(
                data -> {}, // onNext已经在doOnNext中处理
                e -> handleError(emitter, e), // onError
                () -> {} // onComplete已经在doOnComplete中处理
            );
        return emitter;
    }

    private void sendData(SseEmitter emitter, ServerSentEvent data) {
        try {
            // 立即发送数据，不缓冲
            emitter.send(SseEmitter.event()
                .data(data.data() == null ? "{}" : data.data()));
        } catch (Exception e) {
            handleError(emitter, e);
        }
    }

    private void handleError(SseEmitter emitter, Throwable e) {
        // 检查是否为客户端断开连接的异常
        if (isClientDisconnectedException(e)) {
            if (sseConfig.isEnableClientDisconnectDebugLog()) {
                log.debug("Client disconnected during SSE transmission: {}", e.getMessage());
            }
            // 客户端断开连接时，静默完成emitter，不发送错误消息
            try {
                emitter.complete();
                if (sseConfig.isEnableClientDisconnectDebugLog()) {
                    log.debug("SSE emitter completed due to client disconnect");
                }
            } catch (Exception ex) {
                // 忽略完成时的异常，因为连接已经断开
                if (sseConfig.isEnableClientDisconnectDebugLog()) {
                    log.trace("Exception while completing emitter after client disconnect: {}", ex.getMessage());
                }
            }
            return;
        }

        // 处理其他类型的错误
        log.error("SSE emitter handleError - Error Type: {}, Message: {}",
                  e.getClass().getSimpleName(), e.getMessage(), e);
        try {
            JSONObject error = new JSONObject();
            error.put("event", "error");
            error.put("message", ErrorCodeConstants.ERROR_5039.getMsg());
            error.put("code", ErrorCodeConstants.ERROR_5039.getCode());
            error.put("timestamp", System.currentTimeMillis());
            error.put("errorType", e.getClass().getSimpleName());

            emitter.send(SseEmitter.event()
                .name("message") // 设置事件名称
                .data(error.toJSONString()));

            log.info("SSE error message sent successfully");
        } catch (Exception e1) {
            // 检查发送错误消息时是否也是客户端断开
            if (isClientDisconnectedException(e1)) {
                if (sseConfig.isEnableClientDisconnectDebugLog()) {
                    log.debug("Client disconnected while sending error message: {}", e1.getMessage());
                }
            } else {
                log.error("SSE handleError Exception - Failed to send error message", e1);
            }
        } finally {
            try {
                emitter.complete();
                log.info("SSE emitter completed in error handler");
            } catch (Exception e2) {
                if (!isClientDisconnectedException(e2)) {
                    log.error("SSE emitter complete failed in error handler", e2);
                }
            }
        }
    }

    /**
     * 检查异常是否为客户端断开连接导致的
     * @param throwable 异常对象
     * @return true 如果是客户端断开连接异常
     */
    private boolean isClientDisconnectedException(Throwable throwable) {
        if (throwable == null) {
            return false;
        }

        String className = throwable.getClass().getSimpleName();
        String message = throwable.getMessage();

        // 检查常见的客户端断开连接异常
        return "ClientAbortException".equals(className) ||
               "IOException".equals(className) && message != null && (
                   message.contains("Broken pipe") ||
                   message.contains("Connection reset by peer") ||
                   message.contains("Connection aborted") ||
                   message.contains("远程主机强迫关闭了一个现有的连接") ||
                   message.contains("An existing connection was forcibly closed")
               ) ||
               // 检查嵌套的异常
               isClientDisconnectedException(throwable.getCause());
    }

    private void completeEmitter(SseEmitter emitter) {
        try {
            emitter.complete();
        } catch (Exception e) {
            if (isClientDisconnectedException(e)) {
                if (sseConfig.isEnableClientDisconnectDebugLog()) {
                    log.debug("Client disconnected during emitter completion: {}", e.getMessage());
                }
            } else {
                log.error("SSE Emitter Failed to complete", e);
            }
        }
    }

    @Override
    public SseEmitter getEmitter(SseEmitter emitter, ErrorCode errorCode) {
        return getEmitter(emitter, "error", errorCode);
    }

    @Override
    public SseEmitter getEmitter(SseEmitter emitter, String event, ErrorCode errorCode) {
        if (emitter == null) {
            emitter = new SseEmitter(100L);
        }
        JSONObject error = new JSONObject();
        error.put("event", event);
        error.put("message", errorCode.getMsg());
        error.put("code", errorCode.getCode());

        ServerSentEvent data = ServerSentEvent.builder().data(error.toJSONString()).build();
        sendData(emitter, data);
        completeEmitter(emitter);
        return emitter;
    }

    @Override
    public Integer activeDifyAccount(String email, String name, String password) {
        String id = baseMapper.getDifyAccount(email);
        byte[] salt = new byte[16];
        String base64Salt = CommonUtil.getBase64Salt(salt);
        String base64Pwd = CommonUtil.getBase64Pwd(password, salt);
        if (id != null && !id.isEmpty()) {
            return baseMapper.activeDifyAccount(email, base64Salt, base64Pwd);
        } else {
            Integer res = baseMapper.saveDifyAccount(email, name, base64Salt, base64Pwd);
            if (res > BaseConstant.ZERO) {
                id = baseMapper.getDifyAccount(email);
                res = baseMapper.saveTenantAccount(id);
            }

            return res;
        }
    }


    /**
     * 生成带签名的文件预览URL
     * 
     * @param uploadFileId 文件上传ID
     * @return 签名后的文件预览URL
     */
    @Override
    public String getSignedFileUrl(String uploadFileId) {
        String url = BaseConstant.PROD_STR.equals(active) ? DifyUtil.FILES_URL_PROD : DifyUtil.FILES_URL_TEST;
        url += "/files/" + uploadFileId + "/file-preview";
        
        String timestamp = String.valueOf(System.currentTimeMillis() / 1000);
        String nonce = UUID.randomUUID().toString().replace("-", "");
        String msg = "file-preview|" + uploadFileId + "|" + timestamp + "|" + nonce;
        
        try {
            javax.crypto.Mac mac = javax.crypto.Mac.getInstance("HmacSHA256");
            javax.crypto.spec.SecretKeySpec secretKeySpec = new javax.crypto.spec.SecretKeySpec(DifyUtil.SECRET_KEY.getBytes("UTF-8"), "HmacSHA256");
            mac.init(secretKeySpec);
            byte[] sign = mac.doFinal(msg.getBytes("UTF-8"));
            String encodedSign = java.util.Base64.getUrlEncoder().encodeToString(sign);
            
            return url + "?timestamp=" + timestamp + "&nonce=" + nonce + "&sign=" + encodedSign;
        } catch (Exception e) {
            log.error("生成签名URL失败", e);
            throw new RuntimeException("生成签名URL失败", e);
        }
    }

    @Override
    public JSONObject getMessages(String conversationId) {
        if (StringUtils.isBlank(conversationId)) {
            throw exception(ErrorCodeConstants.ERROR_5038);
        }
        UserSessionResponse userSession = baseMapper.getUserSession(conversationId);
        log.info("userSession:{} === {}", conversationId, userSession);
        if (userSession == null || StringUtils.isBlank(userSession.getSessionId()) || StringUtils.isBlank(userSession.getAppId())) {
            throw exception(ErrorCodeConstants.ERROR_5038);
        }

        MessagesRequest param = new MessagesRequest();
        param.setConversation_id(conversationId);
        param.setFirst_id(BaseConstant.EMPTY_STR);
        param.setLimit(100);
        param.setAppId(userSession.getAppId());
        param.setUser(userSession.getSessionId());
        return messages(param);
    }

}
