package cn.iocoder.yudao.aiBase.service.impl;

import cn.iocoder.yudao.aiBase.config.BaseConstant;
import cn.iocoder.yudao.aiBase.config.DBConstant;
import cn.iocoder.yudao.aiBase.dto.param.AiFeeTypeParam;
import cn.iocoder.yudao.aiBase.dto.request.AiFeeTypeSaveReqVO;
import cn.iocoder.yudao.aiBase.entity.AiFeeType;
import cn.iocoder.yudao.aiBase.mapper.AiFeeTypeMapper;
import cn.iocoder.yudao.aiBase.service.AiFeeTypeService;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;

/**
 * AI费用类型 Service实现类
 * 
 * <AUTHOR>
 * @since 2023-09-15
 */
@Slf4j
@Service
@DS(DBConstant.AiBase)
public class AiFeeTypeServiceImpl extends ServiceImpl<AiFeeTypeMapper, AiFeeType> implements AiFeeTypeService {

    @Override
    public List<AiFeeType> selectList(AiFeeTypeParam reqVO) {
        return baseMapper.selectList(reqVO);
    }

    @Override
    public PageResult<AiFeeType> selectPage(AiFeeTypeParam reqVO) {
        return baseMapper.selectPage(reqVO);
    }

    @Override
    public List<AiFeeType> selectListByAppUuid(String appUuid) {
        LambdaQueryWrapper<AiFeeType> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(AiFeeType::getAppUuid, appUuid)
               .orderByAsc(AiFeeType::getOrderNum)
               .orderByDesc(AiFeeType::getId);
        return list(wrapper);
    }

    @Override
    public List<AiFeeType> selectListByAppUuidAndOnline(String appUuid, Integer online) {
        LambdaQueryWrapper<AiFeeType> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(AiFeeType::getAppUuid, appUuid)
               .eq(AiFeeType::getOnline, online)
               .orderByAsc(AiFeeType::getOrderNum)
               .orderByDesc(AiFeeType::getId);
        return list(wrapper);
    }

    @Override
    public AiFeeType selectByPackageKey(String packageKey) {
        LambdaQueryWrapper<AiFeeType> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(AiFeeType::getPackageKey, packageKey);
        return getOne(wrapper);
    }

    @Override
    public AiFeeType selectByPriceId(String priceId) {
        LambdaQueryWrapper<AiFeeType> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(AiFeeType::getPriceId, priceId);
        return getOne(wrapper);
    }

    @Override
    public List<AiFeeType> selectByDisplayName(String displayName) {
        LambdaQueryWrapper<AiFeeType> wrapper = new LambdaQueryWrapper<>();
        wrapper.like(AiFeeType::getDisplayName, displayName)
               .orderByAsc(AiFeeType::getOrderNum)
               .orderByDesc(AiFeeType::getId);
        return list(wrapper);
    }

    @Override
    public Integer createFeeType(AiFeeTypeSaveReqVO createReqVO) {
        // 插入
        AiFeeType feeType = BeanUtils.toBean(createReqVO, AiFeeType.class);
        feeType.setCreatedAt(LocalDateTime.now());
        feeType.setUpdatedAt(LocalDateTime.now());
        baseMapper.insert(feeType);
        // 返回
        return feeType.getId();
    }

    @Override
    public void updateFeeType(AiFeeTypeSaveReqVO updateReqVO) {
        // 校验存在
        validateFeeTypeExists(updateReqVO.getId());
        // 更新
        AiFeeType updateObj = BeanUtils.toBean(updateReqVO, AiFeeType.class);
        updateObj.setUpdatedAt(LocalDateTime.now());
        baseMapper.updateById(updateObj);
    }

    @Override
    public void deleteFeeType(Integer id) {
        // 校验存在
        validateFeeTypeExists(id);
        // 删除
        baseMapper.deleteById(id);
    }

    private void validateFeeTypeExists(Integer id) {
        if (baseMapper.selectById(id) == null) {
            throw new RuntimeException("AI费用类型不存在，ID: " + id);
        }
    }

    @Override
    public AiFeeType getFeeType(Integer id) {
        return baseMapper.selectById(id);
    }

    @Override
    public void toggleFeeTypeStatus(Integer id, Integer online) {
        // 校验存在
        validateFeeTypeExists(id);
        // 校验状态参数
        if (online == null || (online != BaseConstant.ZERO && online != BaseConstant.ONE)) {
            throw new RuntimeException("上架状态参数错误，只能是0（下架）或1（上架）");
        }
        // 更新状态
        LambdaUpdateWrapper<AiFeeType> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(AiFeeType::getId, id)
                .set(AiFeeType::getOnline, online)
                .set(AiFeeType::getUpdatedAt, LocalDateTime.now());
        update(updateWrapper);
    }

    @Override
    public Integer copyFeeType(Integer id) {
        // 校验原费用类型存在
        AiFeeType originalFeeType = getFeeType(id);
        if (originalFeeType == null) {
            throw new RuntimeException("原费用类型不存在，ID: " + id);
        }

        // 复制费用类型
        AiFeeType copyFeeType = BeanUtils.toBean(originalFeeType, AiFeeType.class);
        copyFeeType.setId(null); // 清空ID，让数据库自动生成
        copyFeeType.setOnline(BaseConstant.ZERO); // 默认下架状态
        copyFeeType.setDisplayName(originalFeeType.getDisplayName() + "_副本");
        copyFeeType.setCreatedAt(LocalDateTime.now());
        copyFeeType.setUpdatedAt(LocalDateTime.now());

        // 插入新记录
        baseMapper.insert(copyFeeType);
        return copyFeeType.getId();
    }

}
