package cn.iocoder.yudao.aiBase.controller;

import cn.iocoder.yudao.aiBase.config.BaseConstant;
import cn.iocoder.yudao.aiBase.config.ErrorCodeConstants;
import cn.iocoder.yudao.aiBase.dto.request.dify.ChatMessagesRequest;
import cn.iocoder.yudao.aiBase.dto.request.dify.ConversationsRequest;
import cn.iocoder.yudao.aiBase.dto.request.dify.MessagesRequest;
import cn.iocoder.yudao.aiBase.dto.request.dify.WorkflowsRunRequest;
import cn.iocoder.yudao.aiBase.service.AiAppLangsService;
import cn.iocoder.yudao.aiBase.service.AliyunService;
import cn.iocoder.yudao.aiBase.service.ApiTokensService;
import cn.iocoder.yudao.aiBase.util.DifyUtil;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.ratelimiter.core.annotation.RateLimiter;
import com.alibaba.fastjson.JSONObject;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.MediaType;
import org.springframework.http.codec.ServerSentEvent;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;
import reactor.core.publisher.Flux;

import java.util.List;

import static cn.iocoder.yudao.framework.common.exception.enums.GlobalErrorCodeConstants.INTERNAL_SERVER_ERROR;
import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;


/**
 * 测试接口
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/ai-base/yps-chat")
@Tag(name = "对外 - dify接口")
public class YpsChatController {

    @Autowired
    private ApiTokensService apiTokensService;

    @Autowired
    private AiAppLangsService aiAppLangsService;

    @Autowired
    private AliyunService aliyunService;

    @Value("${spring.profiles.active}")
    private String active;


    @RateLimiter(count = 1, message = "ERROR_5045")
    @GetMapping("/test")
    @Operation(summary = "测试接口")
    @ResponseBody
    public CommonResult<String> test() {
        return CommonResult.success("测试接口");
    }

    /**
     * 创建会话消息
     * @param param
     * @return
     */
    @PostMapping(value = "/chat-messages", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    @Operation(summary = "流模式，创建会话消息")
    public SseEmitter chatMsg(@Valid @RequestBody ChatMessagesRequest param) {
        // 从缓存中获取益普生应用列表
        List<String> list = aiAppLangsService.getAllDifyUuidFromCache(active);
        if (list.contains(param.getAppId())) {
            throw exception(ErrorCodeConstants.ERROR_5027);
        }

        Flux<ServerSentEvent> flux = apiTokensService.chatMsg(param);
        SseEmitter emitter = apiTokensService.getEmitter(flux);
        return emitter;
    }

    /**
     * 获取会话历史消息
     * @param param
     * @return
     */
    @PostMapping("/messages")
    @Operation(summary = "获取会话历史消息")
    public CommonResult<?> messages(@Valid @RequestBody MessagesRequest param) {
        try {
            JSONObject res = apiTokensService.messages(param);

            return CommonResult.success(res);
        } catch (Exception e) {
            return CommonResult.error(INTERNAL_SERVER_ERROR.getCode(), e.getMessage());
        }
    }

    /**
     * 获取会话列表
     * @param appId
     * @param user
     * @param last_id
     * @param limit
     * @return
     */
    @GetMapping("/conversations")
    @Operation(summary = "获取会话列表")
    public CommonResult<?> getConversations(
                                            @RequestParam(value="appId") String appId,
                                            @RequestParam(value="user") String user,
                                            @RequestParam(value = "last_id", required = false) String last_id,
                                            @RequestParam(value = "limit", required = false, defaultValue = "20") Integer limit) {

        try {
            ConversationsRequest param = new ConversationsRequest();
            param.setLast_id(last_id==null? BaseConstant.EMPTY_STR :last_id);
            param.setLimit(limit);
            param.setSort_by("-updated_at");
            param.setAppId(appId);
            param.setUser(user);

            JSONObject res = apiTokensService.conversations(param);

            return CommonResult.success(res);
        } catch (Exception e) {
            return CommonResult.error(INTERNAL_SERVER_ERROR.getCode(), e.getMessage());
        }
    }

    @PostMapping(value = "/workflows/run1")
    @Operation(summary = "阻塞模式，执行workflow")
    @ResponseBody
    public CommonResult<?> workflowsRun1(@Valid @RequestBody WorkflowsRunRequest param) {
        try {
            JSONObject res = apiTokensService.workflowsRun1(param);

            return CommonResult.success(res);
        } catch (Exception e) {
            return CommonResult.error(INTERNAL_SERVER_ERROR.getCode(), e.getMessage());
        }
    }

    /**
     * 执行workflow
     * @param param
     * @return
     */
    @PostMapping(value = "/workflows_run")
    @Operation(summary = "执行workflow")
    public Object workflowsRun(@Valid @RequestBody WorkflowsRunRequest param) {
        // 从缓存中获取益普生应用列表
        List<String> list = aiAppLangsService.getAllDifyUuidFromCache(active);
        if (list.contains(param.getAppId())) {
            throw exception(ErrorCodeConstants.ERROR_5027);
        }

        if (DifyUtil.STREAMING_MODE.equals(param.getResponse_mode())) {
            // 使用一个线程或反应式流来订阅第三方SSE事件并转发到前端
            Flux<ServerSentEvent> flux = apiTokensService.workflowsRun(param);
            SseEmitter emitter = apiTokensService.getEmitter(flux);
            return emitter;
        } else {
            try {
                JSONObject res = apiTokensService.workflowsRun1(param);
                return CommonResult.success(res);
            } catch (Exception e) {
                return CommonResult.error(INTERNAL_SERVER_ERROR.getCode(), e.getMessage());
            }
        }
    }


    @PostMapping(value = "/completion-messages", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    @Operation(summary = "流模式，执行文本完成")
    public SseEmitter completionMessages(@Valid @RequestBody WorkflowsRunRequest param) {
        // 从缓存中获取益普生应用列表
        List<String> list = aiAppLangsService.getAllDifyUuidFromCache(active);
        if (list.contains(param.getAppId())) {
            throw exception(ErrorCodeConstants.ERROR_5027);
        }

        Flux<ServerSentEvent> flux = apiTokensService.completionMsg(param);
        SseEmitter emitter = apiTokensService.getEmitter(flux);
        return emitter;
    }

    @GetMapping("/getAsrToken")
    @Operation(summary = "获取asr token")
    @ResponseBody
    public CommonResult<String> getAsrToken() {
        return CommonResult.success(aliyunService.getAsrToken());
    }



}
