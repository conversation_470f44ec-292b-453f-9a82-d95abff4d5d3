package cn.iocoder.yudao.aiBase.controller;

import cn.iocoder.yudao.aiBase.dto.request.CreateSubReqVO;
import cn.iocoder.yudao.aiBase.dto.response.BindAppUserResponse;
import cn.iocoder.yudao.aiBase.service.*;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.web.bind.annotation.*;

import static cn.iocoder.yudao.framework.common.exception.enums.GlobalErrorCodeConstants.INTERNAL_SERVER_ERROR;

/**
 * 用户订阅
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/ai-base/appUser")
@Tag(name = "用户订阅")
public class AppUserController {

    @Autowired
    private AiAppLangsService aiAppLangsService;

    /**
     * 创建订阅链接
     * @return
     */
    @PostMapping("/createSubscription")
    @Operation(summary = "创建订阅链接")
    public CommonResult<String> createSubscription(@Valid @RequestBody CreateSubReqVO reqVO, @RequestHeader(value= HttpHeaders.AUTHORIZATION) String auth) {
        try {
            return CommonResult.success(aiAppLangsService.createSubscription(auth, reqVO));
        } catch (Exception e) {
            e.printStackTrace();
            return CommonResult.error(INTERNAL_SERVER_ERROR.getCode(), e.getMessage());
        }
    }

    /**
     * 创建订单
     * @return
     */
    @PostMapping("/createOrder")
    @Operation(summary = "创建订单")
    public CommonResult<String> createOrder(@Valid @RequestBody CreateSubReqVO reqVO) {
        try {
            return CommonResult.success(aiAppLangsService.createOrder(reqVO));
        } catch (Exception e) {
            e.printStackTrace();
            return CommonResult.error(INTERNAL_SERVER_ERROR.getCode(), e.getMessage());
        }
    }

    /**
     * 取消订阅
     * @param appUuid
     * @param auth
     * @return
     */
    @PostMapping("/cancelSubscription")
    @Operation(summary = "取消订阅")
    public CommonResult<String> cancelSubscription(@RequestParam(value="appUuid") String appUuid, @RequestParam(value="packageKey", required = false) String packageKey,
                                                   @RequestHeader(value=HttpHeaders.AUTHORIZATION) String auth) {
        try {
            if (StringUtils.isBlank(packageKey)) {
                packageKey = AiAppUserPackageService.PACKAGE_ALL_APPS;
            }
            return CommonResult.success(aiAppLangsService.cancelSubscription(auth, appUuid, packageKey));
        } catch (Exception e) {
            e.printStackTrace();
            return CommonResult.error(INTERNAL_SERVER_ERROR.getCode(), e.getMessage());
        }
    }

    /**
     * 绑定用户和app关系
     * @param appUuid
     * @param auth
     * @return
     */
    @PostMapping("/bindAppUser")
    @Operation(summary = "绑定用户和app关系")
    public CommonResult<BindAppUserResponse> bindAppUser(@RequestParam(value="appUuid") String appUuid, @RequestParam(value="appNameEn") String appNameEn, @RequestHeader(value=HttpHeaders.AUTHORIZATION) String auth) {
        try {
            return CommonResult.success(aiAppLangsService.bindAppUser(auth, appUuid, appNameEn));
        } catch (Exception e) {
            e.printStackTrace();
            return CommonResult.error(INTERNAL_SERVER_ERROR.getCode(), e.getMessage());
        }
    }


}
