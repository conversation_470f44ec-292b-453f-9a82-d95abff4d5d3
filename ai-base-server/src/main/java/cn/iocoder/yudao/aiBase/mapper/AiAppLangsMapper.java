package cn.iocoder.yudao.aiBase.mapper;

import cn.iocoder.yudao.aiBase.dto.param.AiAppParam;
import cn.iocoder.yudao.aiBase.dto.param.AiPageParam;
import cn.iocoder.yudao.aiBase.dto.response.SubscriptionCountResponse;
import cn.iocoder.yudao.aiBase.entity.AiAppLangs;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;


/**
 * 操作记录
 */
@Mapper
public interface AiAppLangsMapper extends BaseMapperX<AiAppLangs> {

    default PageResult<AiAppLangs> selectPage(AiPageParam reqVO) {
        LambdaQueryWrapperX<AiAppLangs> wrapper = new LambdaQueryWrapperX<>();
        wrapper
                .and(reqVO.getKeyword() != null, w -> w
                        .like(AiAppLangs::getAppName, reqVO.getKeyword())
                        .or()
                        .like(AiAppLangs::getAppNameEn, reqVO.getKeyword())
                        .or()
                        .like(AiAppLangs::getAppDescription, reqVO.getKeyword())
                        .or()
                        .like(AiAppLangs::getAppUuid, reqVO.getKeyword())
                        .or()
                        .like(AiAppLangs::getDifyAppUuid, reqVO.getKeyword()))
                .orderByDesc(AiAppLangs::getId)
                .ne(AiAppLangs::getAppStatus, "删除");

        return selectPage(reqVO, wrapper);
    }

    default List<AiAppLangs> selectList(AiAppParam reqVO) {
        LambdaQueryWrapperX<AiAppLangs> wrapper = new LambdaQueryWrapperX<>();
        wrapper
                .eqIfPresent(AiAppLangs::getAppUuid, reqVO.getAppUuid())
                .eqIfPresent(AiAppLangs::getAppNameEn, reqVO.getAppNameEn())
                .inIfPresent(AiAppLangs::getAppNameEn, reqVO.getAppNameEns())
                .eqIfPresent(AiAppLangs::getAppLang, reqVO.getAppLang())
                .eqIfPresent(AiAppLangs::getAppType, reqVO.getAppType())
                .eqIfPresent(AiAppLangs::getDifyAppUuid, reqVO.getDifyAppUuid())
                .eqIfPresent(AiAppLangs::getAppStatus, reqVO.getAppStatus())
                .eqIfPresent(AiAppLangs::getLang, reqVO.getLang())
                .inIfPresent(AiAppLangs::getIsInternalUser, reqVO.getIsInternalUsers())
                .orderByDesc(reqVO.getOrder()!=null && reqVO.getOrder() ==1, AiAppLangs::getUseNum)
                .orderByDesc(reqVO.getOrder()!=null && reqVO.getOrder() ==2, AiAppLangs::getClickNum)
                .ne(AiAppLangs::getAppStatus, "删除")
//                .orderByDesc(AiAppLangs::getIsInternalUser)
                .orderBy(true, reqVO.getIsAscId()==null?false:reqVO.getIsAscId(), AiAppLangs::getId)
                .last(reqVO.getLastLimit()!=null && reqVO.getLastLimit() > 0, "limit "+reqVO.getLastLimit());

        return selectList(wrapper);
    }
    
    default List<AiAppLangs> getSiteMapList() {
        LambdaQueryWrapperX<AiAppLangs> wrapper = new LambdaQueryWrapperX<>();
        wrapper.eq(AiAppLangs::getAppStatus, "上架")
                .ne(AiAppLangs::getAppLang, "中文")
                .select(AiAppLangs::getAppUuid, AiAppLangs::getModificationTime);
        return selectList(wrapper);
    }

    List<SubscriptionCountResponse> countSubscriptions();
}