package cn.iocoder.yudao.aiBase.service;

import cn.iocoder.yudao.aiBase.config.BaseIService;
import cn.iocoder.yudao.aiBase.dto.param.AiFeeTypeParam;
import cn.iocoder.yudao.aiBase.dto.request.AiFeeTypeSaveReqVO;
import cn.iocoder.yudao.aiBase.entity.AiFeeType;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import jakarta.validation.Valid;

import java.util.List;

/**
 * AI费用类型 Service接口
 * 
 * <AUTHOR>
 * @since 2023-09-15
 */
public interface AiFeeTypeService extends BaseIService<AiFeeType, AiFeeTypeParam> {

    /**
     * 根据参数查询AiFeeType列表
     * @param reqVO 请求参数
     * @return AiFeeType列表
     */
    List<AiFeeType> selectList(AiFeeTypeParam reqVO);

    /**
     * 根据参数分页查询AiFeeType列表
     * @param reqVO 请求参数
     * @return AiFeeType分页结果
     */
    PageResult<AiFeeType> selectPage(AiFeeTypeParam reqVO);

    /**
     * 根据应用UUID查询费用类型列表
     * @param appUuid 应用UUID
     * @return AiFeeType列表
     */
    List<AiFeeType> selectListByAppUuid(String appUuid);

    /**
     * 根据应用UUID和在线状态查询费用类型列表
     * @param appUuid 应用UUID
     * @param online 在线状态 1开启，0下架
     * @return AiFeeType列表
     */
    List<AiFeeType> selectListByAppUuidAndOnline(String appUuid, Integer online);

    /**
     * 根据套餐key查询费用类型
     * @param packageKey 套餐key
     * @return AiFeeType对象
     */
    AiFeeType selectByPackageKey(String packageKey);

    /**
     * 根据价格ID查询费用类型
     * @param priceId 价格ID
     * @return AiFeeType对象
     */
    AiFeeType selectByPriceId(String priceId);

    /**
     * 根据显示名称查询费用类型列表
     * @param displayName 显示名称
     * @return AiFeeType列表
     */
    List<AiFeeType> selectByDisplayName(String displayName);

    /**
     * 创建AI费用类型
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Integer createFeeType(@Valid AiFeeTypeSaveReqVO createReqVO);

    /**
     * 更新AI费用类型
     *
     * @param updateReqVO 更新信息
     */
    void updateFeeType(@Valid AiFeeTypeSaveReqVO updateReqVO);

    /**
     * 删除AI费用类型
     *
     * @param id 编号
     */
    void deleteFeeType(Integer id);

    /**
     * 获得AI费用类型
     *
     * @param id 编号
     * @return AI费用类型
     */
    AiFeeType getFeeType(Integer id);



}
