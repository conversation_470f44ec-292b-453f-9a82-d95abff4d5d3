package cn.iocoder.yudao.aiBase.service;

import cn.iocoder.yudao.aiBase.config.BaseIService;
import cn.iocoder.yudao.aiBase.dto.param.UserPackageParam;
import cn.iocoder.yudao.aiBase.dto.response.AiUserPackageResponse;
import cn.iocoder.yudao.aiBase.dto.response.FeeTypeVo;
import cn.iocoder.yudao.aiBase.entity.AiAppUserPackage;
import cn.iocoder.yudao.framework.common.exception.ErrorCode;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.system.api.oauth2.dto.OAuth2AccessTokenCheckRespDTO;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

public interface AiAppUserPackageService extends BaseIService<AiAppUserPackage, UserPackageParam> {

    public static final String PACKAGE_NAME = "sub_packages";
    public static final String PACKAGE_ALL_APPS = "all_apps"; //  所有中文应用
    public static final String FREE_NUM = "free_num";
    public static final String ACTIVITY_FREE_NUM = "activity_apps:free_num";


    PageResult<AiAppUserPackage> selectPage(UserPackageParam reqVO);

    /**
     * 查询用户套餐列表
     *
     * @param reqVO 请求参数
     * @return 用户套餐列表
     */
    List<AiAppUserPackage> selectList(UserPackageParam reqVO);

    /**
     * 根据用户ID、openID、套餐键名和类型获取用户套餐信息
     *
     * @param socialUserId 主站用户ID
     * @param socialType       三方用户ID
     * @param packageKey   套餐键名
     * @param packageType  套餐类型
     * @return 用户套餐信息
     */
    AiAppUserPackage getUserPackage(Long socialUserId, Integer socialType, String packageKey, String packageType);

    /**
     * 根据用户ID和openID获取用户套餐信息
     *
     * @param socialUserId 主站用户ID
     * @param socialType       三方用户ID
     * @return 用户套餐信息
     */
    AiAppUserPackage getUserPackage(Long socialUserId, Integer socialType);

    /**
     * 根据套餐键名和类型获取用户套餐信息
     *
     * @param packageKey 套餐键名
     * @param packageType 套餐类型
     * @return 用户套餐信息
     */
    AiAppUserPackage getUserPackage(String packageKey, String packageType);

    /**
     * 根据用户ID、openID、套餐键名和类型获取用户套餐信息
     *
     * @param packageKey   套餐键名
     * @param packageType  套餐类型
     * @param lang 语言
     * @return 用户套餐信息
     */
    AiAppUserPackage getUserPackage(String packageKey, String packageType, String lang);

    /**
     * 根据用户ID、openID、套餐键名和类型获取用户套餐信息
     * @param packageKey   套餐键名
     * @param packageType  套餐类型
     * @return 用户套餐信息
     */
    AiAppUserPackage getOrCreateUserPackage(String packageKey, String packageType, String lang);

    /**
     * 根据用户ID、openID、套餐键名和类型获取用户套餐信息
     *
     * @param socialUserId 主站用户ID
     * @param socialType       三方用户ID
     * @param packageKey   套餐键名
     * @param packageType  套餐类型
     * @param lang 语言
     * @return 用户套餐信息
     */
    AiAppUserPackage getUserPackage(Long socialUserId, Integer socialType, String packageKey, String packageType, String lang);

    /**
     * 根据用户ID和openID和套餐键名获取订阅中的用户套餐信息
     *
     * @param socialUserId 主站用户ID
     * @param socialType       三方用户ID
     * @param packageKey   套餐键名
     * @return 订阅中的用户套餐信息
     */
    AiAppUserPackage getUserPackageOnSub(Long socialUserId, Integer socialType, String packageKey);


    /**
     * 根据用户ID、openID和套餐键名获取已取消订阅的用户套餐信息
     *
     * @param socialUserId 主站用户ID
     * @param socialType       三方用户ID
     * @param packageKey   套餐键名
     * @return 已取消订阅的用户套餐信息
     */
    AiAppUserPackage getUserPackageOnCancel(Long socialUserId, Integer socialType, String packageKey);

    /**
     * 根据用户ID、socialType和套餐键名获取订阅中的或已取消订阅的用户套餐信息
     *
     * @param socialUserId 主站用户ID
     * @param socialType       三方用户ID
     * @param packageKey   套餐键名
     * @return 订阅中的或已取消订阅的用户套餐信息
     */
    AiAppUserPackage getSubOrCancel(Long socialUserId, Integer socialType, String packageKey);

    /**
     * 根据会话ID获取用户套餐信息
     *
     * @param checkoutSessionId 会话ID
     * @return 用户套餐信息
     */
    AiAppUserPackage getByCheckoutSessionId(String checkoutSessionId);

    /**
     * 获取或创建用户套餐信息
     *
     * @param socialUserId 主站用户ID
     * @param socialType       三方用户ID
     * @param packageKey   套餐键名
     * @param packageType  套餐类型
     * @return 用户套餐信息
     */
    AiAppUserPackage getOrCreateUserPackage(Long socialUserId, Integer socialType, String packageKey, String packageType);

    /**
     * 处理定时任务
     *
     * @param end 结束时间
     */
    void handleTask(LocalDateTime end);

    /**
     * 获取用户套餐响应
     *
     * @param configKey 配置键
     * @param locale
     * @return 用户套餐响应信息
     */
    AiUserPackageResponse getPackageByKey(String configKey, String locale);

    /**
     * 获取用户套餐响应
     *
     * @param configKey 配置键
     * @param locale
     * @return 用户套餐响应信息
     */
    Map<String, AiUserPackageResponse> getPackageByDomain(String configKey, String locale);

    /**
     * 获取费用类型信息
     *
     * @param packageKey 套餐键名
     * @param packageType 套餐类型
     * @return 费用类型信息
     */
    FeeTypeVo getFeeType(String packageKey, String packageType);


    /**
     * 根据套餐键名获取 上架 费用类型列表
     *
     * @param configKey 套餐键名
     * @return 费用类型列表
     */
    List<FeeTypeVo> getFeeTypes(String configKey);

    /**
     * 支付宝订阅 根据套餐键名和套餐类型获取周期类型，默认值是 MONTH
     *
     * @param packageKey 套餐键名
     * @param packageType 套餐类型
     * @return 周期类型，例如 "DAY"、"MONTH"、"PER_USE" 等
     */
    String getPeriodType(String packageKey, String packageType);

    /**
     * 预检查操作
     *
     * @param authUser 认证用户信息
     * @param appUuid 应用UUID
     * @param requestId 请求ID
     * @return 错误码信息
     */
    ErrorCode preCheck(OAuth2AccessTokenCheckRespDTO authUser, String appUuid, String requestId);

    /**
     * 初始化用户套餐
     *
     * @param locale 语言
     * @param socialUserId 用户ID
     * @param socialType 社交类型
     * @param packageKey 套餐Key
     * @param packageType 套餐类型
     * @param startAt 开始时间
     * @param expireAt 过期时间
     */
    void initUserPackage(String locale, Long socialUserId, Integer socialType, String packageKey, String packageType,
                         LocalDateTime startAt, LocalDateTime expireAt);


}
