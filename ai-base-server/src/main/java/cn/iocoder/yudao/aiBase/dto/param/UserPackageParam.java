package cn.iocoder.yudao.aiBase.dto.param;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class UserPackageParam extends AiPageParam {
    @Schema(description = "主站用户ID，登录时必传")
    private Long socialUserId;

    @Schema(description =  "三方类型")
    private Integer socialType;

    @Schema(description =  "语言")
    private String lang;

    @Schema(description =  "套餐键名")
    private String packageKey;

    @Schema(description =  "套餐类型")
    private String packageType;

    @Schema(description =  "sessionId")
    private String checkoutSessionId;

    @Schema(description =  "subStatus")
    private Integer subStatus;

    @Schema(description =  "过期时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] expiredAt;
}
