package cn.iocoder.yudao.aiBase.controller;

import cn.hutool.http.HttpUtil;
import cn.iocoder.yudao.aiBase.config.BaseConstant;
import cn.iocoder.yudao.aiBase.config.ErrorCodeConstants;
import cn.iocoder.yudao.aiBase.dto.alipay.Result;
import cn.iocoder.yudao.aiBase.dto.asr.ASRParam;
import cn.iocoder.yudao.aiBase.dto.asr.RecognitionParam;
import cn.iocoder.yudao.aiBase.dto.param.AiAppParam;
import cn.iocoder.yudao.aiBase.dto.param.IndexParam;
import cn.iocoder.yudao.aiBase.dto.request.*;
import cn.iocoder.yudao.aiBase.dto.request.dify.AppsParamsRequest;
import cn.iocoder.yudao.aiBase.dto.request.dify.ConversationsRequest;
import cn.iocoder.yudao.aiBase.dto.request.dify.DifyBaseRequest;
import cn.iocoder.yudao.aiBase.dto.request.dify.MedsciUserRequest;
import cn.iocoder.yudao.aiBase.dto.response.*;
import cn.iocoder.yudao.aiBase.entity.AiAppLangs;
import cn.iocoder.yudao.aiBase.entity.MedsciUsers;
import cn.iocoder.yudao.aiBase.mq.AsrDataProducer;
import cn.iocoder.yudao.aiBase.mq.EventDataMsg;
import cn.iocoder.yudao.aiBase.mq.EventDataProducer;
import cn.iocoder.yudao.aiBase.service.*;
import cn.iocoder.yudao.aiBase.util.*;
import cn.iocoder.yudao.framework.common.exception.enums.GlobalErrorCodeConstants;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.servlet.ServletUtils;
import cn.iocoder.yudao.module.infra.controller.admin.config.vo.ConfigPageReqVO;
import cn.iocoder.yudao.module.infra.controller.admin.config.vo.ConfigRespVO;
import cn.iocoder.yudao.module.system.api.oauth2.dto.OAuth2AccessTokenCheckRespDTO;
import com.alibaba.fastjson.JSONObject;
import com.xingyuv.jushauth.model.AuthResponse;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.dromara.hutool.core.net.url.UrlEncoder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.InputStreamResource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.util.*;
import java.util.concurrent.ExecutionException;

import static cn.iocoder.yudao.framework.common.exception.enums.GlobalErrorCodeConstants.INTERNAL_SERVER_ERROR;
import static cn.iocoder.yudao.framework.common.exception.enums.GlobalErrorCodeConstants.UNAUTHORIZED;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

/**
 * 测试接口
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/ai-base/index")
@Tag(name = "用户 - index接口")
public class IndexController {

    @Autowired
    private TestService testService;

    @Autowired
    private MedsciUsersService medsciUsersService;

    @Autowired
    private ApiTokensService apiTokensService;

    @Autowired
    private AiAppLangsService aiAppLangsService;

    @Autowired
    private AiAppUserPackageService aiAppUserPackageService;

    @Autowired
    private AiAppUsageRecordsService aiAppUsageRecordsService;

    @Autowired
    private AiActivityCollectService aiActivityCollectService;

    @Autowired
    private OauthService oauthService;

    @Autowired
    private YudaoSystemService yudaoSystemService;

    @Value("${domain-config.web-home}")
    private String webHome;

    @Value("${dify-base.openapi-host}")
    private String openapiHost;

    @Autowired
    private EventDataProducer eventDataProducer;

    @Autowired
    private AsrDataProducer asrDataProducer;


    @Autowired
    private RecognitionService recognitionService;

    /**
     * 测试接口
     * @param param
     * @return
     */
    @PostMapping("/test")
    @Operation(summary = "测试接口")
    public CommonResult<?> test(@RequestBody IndexParam param1) {

        String res = "";
        if (param1.getAge()>0) {
            String filePath = "/Users/<USER>/Documents/java/ai-base-main/ai-base-server/src/main/resources/temp/test.mp3";
            ASRParam param = new ASRParam();
            param.setProducer(asrDataProducer);
            param.setHost("127.0.0.1");
            param.setPort("10095");
            param.setAudioIn(filePath);
            param.setMode("offline");
            param.setNumThreads(1);
            param.setHotwords("{\"时光\":20,\"谢谢\":20,\"设置\":30}");

            FunasrWsUtil.sendToFunAsr(param);
        } else {
            RecognitionParam param = new RecognitionParam();
            try {
                recognitionService.recognition2(null, param);
            } catch (IOException e) {
                throw new RuntimeException(e);
            } catch (ExecutionException e) {
                throw new RuntimeException(e);
            } catch (InterruptedException e) {
                throw new RuntimeException(e);
            }
        }


	    return CommonResult.success(res);
    }

    /**
     * 查询项目
     * @return
     */
    @PostMapping("/getJwtToken")
    @Operation(summary = "获取jwt token")
    public CommonResult<String> getJwtToken(@RequestBody GetJwtTokenRequest request) {
        String userId = request.getUserId();
        if (request.getToken() != null && request.getToken().length()>0) {
            OAuth2AccessTokenCheckRespDTO res = oauthService.checkAccessToken(request.getToken());
            userId = res.getUserId().toString();
        }
        if (userId == null || userId.length()==0) {
            return CommonResult.error(GlobalErrorCodeConstants.INTERNAL_SERVER_ERROR);
        }
        request.setRoles(Arrays.asList("tenant_admin"));

        String token = JwtTokenUtil.hasuraTwt(request.getRoles(), userId, 30);
        return CommonResult.success(token);
    }
    /**
     * 获取会话列表
     * @param param
     * @return
     */
    @PostMapping("/conversations")
    @Operation(summary = "获取会话列表")
    public CommonResult<?> conversations(@Valid @RequestBody ConversationsRequest param) {
        try {
            JSONObject res = apiTokensService.conversations(param);

            return CommonResult.success(res);
        } catch (Exception e) {
            return CommonResult.error(INTERNAL_SERVER_ERROR.getCode(), e.getMessage());
        }
    }

    /**
     * 获取所有的sitemap地址
     * @return
     */
    @GetMapping("/getSiteMap")
    @Operation(summary = "sitemap查询所有")
    public CommonResult<?> getSiteMap() {
        try {
            List<AiAppLangs> aiAppLangs = aiAppLangsService.getSiteMapList();
            aiAppLangs.forEach(app -> {
                app.setAppUuid(app.getAppUuid());
                app.setAppName(app.getAppName());
                app.setAppNameEn(app.getAppNameEn());
                app.setAppDescription(app.getAppDescription());
                app.setAppIcon(app.getAppIcon());
                app.setAppStatus(app.getAppStatus());
                app.setAppType(app.getAppType());
                app.setAppLang(app.getAppLang());
            });
            return CommonResult.success(aiAppLangs);
        } catch (Exception e) {
            return CommonResult.error(INTERNAL_SERVER_ERROR.getCode(), e.getMessage());
        }
    }

    /**
     * 获取app配置-数组
     * @param param
     * @return
     */
    @PostMapping("/appsParams")
    @Operation(summary = "获取apps配置")
    public CommonResult<?> appsParams(@Valid @RequestBody AppsParamsRequest param) {
        try {
            List<DifyBaseResponse> res = apiTokensService.getAppsPrePrompt(param.getAppIds());
            return CommonResult.success(res);
        } catch (Exception e) {
            return CommonResult.error(ErrorCodeConstants.ERROR_5036);
        }
    }
    /**
     * 校验token
     * @return
     */
    @GetMapping("/checkToken")
    @Operation(summary = "校验jwt token")
    public CommonResult<Boolean> checkToken(@RequestParam("token") String token) {
        Boolean res = JwtTokenUtil.checkToken(token);
        return CommonResult.success(res);
    }

    @PostMapping("/getAiWriteToken")
    @Operation(summary = "获取前台Token")
    public CommonResult<TokenResponse> getAiWriteToken(@RequestBody MedsciUserRequest param,
                                                       @RequestParam(value = "locale", required = false, defaultValue = BaseConstant.ZH) String locale) {
        if (param.getSocialUserId()==null||StringUtils.isBlank(param.getOpenid())||StringUtils.isBlank(param.getUserName())) {
            return CommonResult.error(UNAUTHORIZED);
        }

        TokenResponse res = medsciUsersService.getAiWriteToken(param, locale);
        // 如果来源是主站，则直接订阅传入应用的试用版
        if (BaseConstant.FROM_MEDSCI.equals(param.getFromPlatform()) && StringUtils.isNotBlank(param.getAppUuid())) {
            aiAppLangsService.createAutoSubscription(param.getSocialUserId(), param.getOpenid(), param.getAppUuid(), locale);
        }

        return CommonResult.success(res);
    }

    /**
     * 根据标签获取应用
     * @param tag
     * @return
     */
    @GetMapping("/getAppsByTag")
    @Operation(summary = "根据标签获取应用")
    public CommonResult<?> getAppsByTag(@RequestParam("tag") String tag) {
        try {
            List<AppBaseResponse> res = apiTokensService.getAppsByTag(tag);

            return CommonResult.success(res);
        } catch (Exception e) {
            return CommonResult.error(INTERNAL_SERVER_ERROR.getCode(), e.getMessage());
        }
    }

    /**
     * 获取app分类
     * @return
     */
    @GetMapping("/getAppTypes")
    @Operation(summary = "获取app分类")
    public CommonResult<?> getAppTypes() {
        return CommonResult.success(yudaoSystemService.getDictDataListByDictType("ai_base_app_type"));
    }

    /**
     * 获取app语言
     * @return
     */
    @GetMapping("/getAppLangs")
    @Operation(summary = "获取app语言")
    public CommonResult<?> getAppLangs() {
        return CommonResult.success(yudaoSystemService.getDictDataListByDictType("ai_base_langs"));
    }

    @GetMapping("/getAppByUuid")
    @Operation(summary = "根据uuid查询应用,兼容名称和uuid")
    public CommonResult<AiAppResponse> getAppByUuid(@RequestParam("appUuid") String appUuid, @RequestHeader(value=HttpHeaders.AUTHORIZATION, required = false) String auth,
                                                    @RequestParam(value = "locale", required = false, defaultValue = BaseConstant.ZH) String locale) {
        try {
            if (appUuid.length() < 32) {
                AiAppLangs app = aiAppLangsService.getByNameEn(appUuid, locale);
                appUuid = app.getAppUuid();
            }

            AiAppResponse res = aiAppLangsService.getByUuid(appUuid, auth);
            if (res == null) {
                return CommonResult.error(ErrorCodeConstants.ERROR_5027);
            }

            return CommonResult.success(res);
        } catch (Exception e) {
            e.printStackTrace();
            return CommonResult.error(ErrorCodeConstants.ERROR_5027);
        }
    }

    @GetMapping("/getAppByNameEn")
    @Operation(summary = "根据nameEn查询应用")
    public CommonResult<AiAppResponse> getAppByNameEn(@RequestParam("appUuid") String appUuid, @RequestHeader(value=HttpHeaders.AUTHORIZATION, required = false) String auth,
                                                      @RequestParam(value = "locale", required = false, defaultValue = BaseConstant.ZH) String locale) {
        try {
            AiAppLangs app = aiAppLangsService.getByNameEn(appUuid, locale);
            AiAppResponse res = aiAppLangsService.getByUuid(app.getAppUuid(), auth);
            if (res == null) {
                return CommonResult.error(ErrorCodeConstants.ERROR_5027);
            }

            return CommonResult.success(res);
        } catch (Exception e) {
            e.printStackTrace();
            return CommonResult.error(ErrorCodeConstants.ERROR_5027);
        }
    }

    /**
     * 获取app配置
     * @param param
     * @return
     */
    @PostMapping("/appParams")
    @Operation(summary = "获取单个app配置")
    public CommonResult<?> appParams(@Valid @RequestBody DifyBaseRequest param) {
        try {
            DifyBaseResponse res = apiTokensService.getAppPrePrompt(param.getAppId());
            return CommonResult.success(res);
        } catch (Exception e) {
            return CommonResult.error(ErrorCodeConstants.ERROR_5036);
        }
    }
    /**
     * 记录请求dify报错
     * @return
     */
    @PostMapping("/chatMsgError")
    @Operation(summary = "记录请求dify报错")
    public CommonResult<?> chatMsgError() {
        return CommonResult.success(true);
    }

    @PostMapping("/getAppList")
    @Operation(summary = "获取前台应用列表")
    public CommonResult<?> getAppList(@RequestBody AiAppParam param, @RequestHeader(value=HttpHeaders.AUTHORIZATION, required = false) String auth) {
        if (param.getIsMine()==null) {
            param.setIsMine(BaseConstant.TWO);
        }
        List<AiAppResponse> res = aiAppLangsService.getAppList(param, auth);

        return CommonResult.success(res);
    }

    @GetMapping("/refreshAppToken")
    @Operation(summary = "刷新token")
    public CommonResult<?> refreshAppToken() {
        apiTokensService.refreshToken();
        return CommonResult.success(true);
    }

    @GetMapping("/getLocation")
    @Operation(summary = "根据ip获取国家")
    public CommonResult<String> getLocation(HttpServletRequest request) {
        String ip = CommonUtil.getIpAddress(request);
        try {
            String country = GeoLiteUtil.getCountry(ip);
            log.info("根据ip获取国家:{} {}", ip, country);
            return CommonResult.success(country);
        } catch (Exception e) {
            log.error("根据ip获取国家:{} {}", ip, e.getMessage());
            return CommonResult.success("中国");
        }
    }

    @GetMapping("/getConfigPage")
    @Operation(summary = "获取国际化语言配置")
    public CommonResult<PageResult<ConfigRespVO>> getConfigPage(@RequestParam(value="configName", required = false) String configName,
                                                                @RequestParam(value="configKey", required = false) String configKey) {
        ConfigPageReqVO pageReqVO = new ConfigPageReqVO();
        pageReqVO.setName(StringUtils.isNotBlank(configName)?configName:"aiBase国际化");
        pageReqVO.setKey(StringUtils.isNotBlank(configKey)?configKey:"");
        List<ConfigRespVO> configCache = yudaoSystemService.getConfigCache(pageReqVO);
        PageResult<ConfigRespVO> pageResult = new PageResult<>(configCache, Long.valueOf(configCache.size()));
        return success(pageResult);
    }

    @GetMapping("/socialAuthRedirect")
    @Operation(summary = "社交授权的跳转，1小时有效，详见SocialTypeEnum，35谷歌 36Facebook 55谷歌xy 56FBxy")
    public CommonResult<String> socialAuthRedirect(@RequestParam("socialType") Integer socialType) {
        try {
            String res = oauthService.getSocialAuthorizeUrl(socialType);

            return CommonResult.success(res);
        } catch (Exception e) {
            e.printStackTrace();
            return CommonResult.error(INTERNAL_SERVER_ERROR.getCode(), e.getMessage());
        }
    }

    @GetMapping("/socialLogin")
    @Operation(summary = "社交快捷登录，使用 code 授权码", description = "适合未登录的用户，但是社交账号已绑定用户，已迁移到Auth-server了")
    public CommonResult<?> socialLogin(@RequestParam("socialType") Integer socialType, @RequestParam("code") String code, @RequestParam("state") String state) {
        try {
            return CommonResult.success(medsciUsersService.socialLogin(socialType, code, state));
        } catch (Exception e) {
            e.printStackTrace();
            return CommonResult.error(INTERNAL_SERVER_ERROR.getCode(), e.getMessage());
        }
    }

    @GetMapping("/getAppClickNum")
    @Operation(summary = "获取app点击数量")
    public CommonResult<Integer> getAppClickNum(@RequestParam("appUuid") String appUuid, @RequestParam("openid") String openid) {
        appUuid = appUuid.replace("-", "");
        Integer num = aiAppLangsService.getAppClickNum(appUuid, openid);
        return CommonResult.success(num);
    }

    @PostMapping("/logout")
    @Operation(summary = "退出")
    public CommonResult<?> logout(@RequestHeader(value=HttpHeaders.AUTHORIZATION, required = false) String auth, HttpServletResponse response, HttpServletRequest request) {
        try {
            medsciUsersService.logout(response, request, auth);
            return CommonResult.success(true);
        } catch (Exception e) {
            e.printStackTrace();
            return CommonResult.success(true);
        }
    }

    @PostMapping("/oauthLogin/{socialType}")
    @Operation(summary = "授权服务推送回调信息")
    public CommonResult<?> oauthLogin(@PathVariable Integer socialType, @RequestParam("code") String code, @RequestParam("state") String state,
                             @RequestBody AuthResponse authResponse) {
        String refer = oauthService.getRefererFromCache(state);
        try {
            String userInfo = oauthService.getRawUserInfo(socialType, state, code, authResponse);
            medsciUsersService.getUser(socialType, userInfo);

            return CommonResult.success(refer);
        } catch (Exception e) {
            return CommonResult.error(INTERNAL_SERVER_ERROR.getCode(), refer+"?msg="+UrlEncoder.encodeAll(e.getMessage()));
        }
    }

    @PostMapping("/webhook")
    @Operation(summary = "接收事件推送")
    public CommonResult<String> webhook(HttpServletRequest request) {
        eventDataProducer.send(EventDataMsg.builder().sigHeader(request.getHeader("Stripe-Signature")).payload(ServletUtils.getBody(request)).build());
        return CommonResult.success("");
    }

    /**
     * 支付宝订阅/取消通知
     * @param request
     * @return
     */
    @PostMapping("/alipaySubNotify")
    @Operation(summary = "接收支付宝订阅/取消通知")
    public Result<String> alipaySubNotify(HttpServletRequest request) {
        eventDataProducer.send(EventDataMsg.builder().sigHeader(MedsciSubUtil.AlipaySubEvent).payload(ServletUtils.getBody(request)).build());
        return Result.success("");
    }

    @PostMapping("/getSubLog")
    @Operation(summary = "主站来查询订单")
    public Result<?> getSubLog(@RequestBody GetSubLogReqVO reqVO) {
        try {
            return Result.success(aiAppLangsService.getSubLog(reqVO.getAppOrderId()));
        } catch (Exception e) {
            return Result.error(e.getMessage());
        }
    }

    /**
     * 邮箱验证码
     * @return
     */
    @PostMapping("/sendEmailCode")
    @Operation(summary = "邮箱验证码")
    public CommonResult<Boolean> sendEmailCode(@RequestParam("email") String email, @RequestParam("type") String type,
                                               @RequestParam(value = "socialType", required = false, defaultValue = "37") Integer socialType) {
        try {
            if (!CommonUtil.checkEmail(email)) {
                return CommonResult.error(ErrorCodeConstants.ERROR_5002);
            }
            if (type.equals("RegisterCode")) {
                if (medsciUsersService.getByEmail(email, socialType) != null) {
                    return CommonResult.error(ErrorCodeConstants.ERROR_5003);
                }
                yudaoSystemService.sendEmail(email, socialType);
            }

            return CommonResult.success(true);
        } catch (Exception e) {
            return CommonResult.error(INTERNAL_SERVER_ERROR.getCode(), e.getMessage());
        }
    }

    /**
     * 注册
     * @return
     */
    @PostMapping("/register")
    @Operation(summary = "注册")
    public CommonResult<Boolean> register(@Valid @RequestBody RegisterReqVO registerReqVO) {
        try {
            return CommonResult.success(medsciUsersService.register(registerReqVO));
        } catch (Exception e) {
            return CommonResult.error(INTERNAL_SERVER_ERROR.getCode(), e.getMessage());
        }
    }

    /**
     * 登录
     * @return
     */
    @PostMapping("/login")
    @Operation(summary = "邮箱登录")
    public CommonResult<TokenResponse> login(@Valid @RequestBody LoginReqVO loginReqVO) {
        try {
            return CommonResult.success(medsciUsersService.login(loginReqVO));
        } catch (Exception e) {
            return CommonResult.error(INTERNAL_SERVER_ERROR.getCode(), e.getMessage());
        }
    }

    /**
     * 创建支付宝订阅
     * @param piId
     * @param socialUserId
     * @param openid
     * @return
     */
    @GetMapping("/createAliSub")
    @Operation(summary = "创建支付宝订阅")
    public CommonResult<?> createAliSub(@RequestParam("piId") String piId, @RequestParam("sessionId") String sessionId,
                                        @RequestParam("socialUserId") Long socialUserId, @RequestParam("openid") String openid) {
        try {
            return CommonResult.success(aiAppLangsService.createAliSub(piId, sessionId, socialUserId, openid));
        } catch (Exception e) {
            return CommonResult.error(INTERNAL_SERVER_ERROR.getCode(), e.getMessage());
        }
    }

    @GetMapping("/getPubmedRis")
    @Operation(summary = "获取pubmed文件下载")
    public ResponseEntity<InputStreamResource> getPubmedRis(@RequestParam("pmid") String pmid) throws Exception {
        return OpenapiUtil.getPubmedRis(openapiHost, pmid);
    }

    @GetMapping("/pubmed/ris/{pmid}")
    public ResponseEntity<InputStreamResource> downloadPubmedRis(@PathVariable String pmid) {
        // 构造目标URL
        String targetUrl = "https://eutils.ncbi.nlm.nih.gov/entrez/eutils/efetch.fcgi?db=pubmed&id=" + pmid + "&rettype=ris&retmode=text";

        try {
            // 使用Hutool下载文件
            byte[] fileBytes = HttpUtil.downloadBytes(targetUrl);

            // 设置响应头
            HttpHeaders headers = new HttpHeaders();
            headers.add(HttpHeaders.USER_AGENT, "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/111.0.1661.44");
            headers.add(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=pubmed_" + pmid + ".ris");
            headers.setContentType(MediaType.TEXT_PLAIN);

            // 返回文件流响应
            return new ResponseEntity<>(new InputStreamResource(new ByteArrayInputStream(fileBytes)), headers, HttpStatus.OK);
        } catch (Exception e) {
            // 记录错误日志
            e.printStackTrace(); // 或者使用SLF4J Logger

            // 返回错误响应
            return new ResponseEntity<>(HttpStatus.BAD_REQUEST);
        }
    }

    @GetMapping("/getHToken")
    @Operation(summary = "获取未登录hToken")
    public CommonResult<TokenResponse> getHToken() {
        MedsciUsers user = new MedsciUsers();
        user.setOpenid("NoLoginUser");
        return CommonResult.success(medsciUsersService.getToken(user));
    }

    @GetMapping("/getSubOrder")
    @Operation(summary = "查询订单状态")
    public CommonResult<?> getSubOrder(@RequestParam("piId") String piId) {
        try {
            return CommonResult.success(aiAppLangsService.getSubLog(piId));
        } catch (Exception e) {
            return CommonResult.error(INTERNAL_SERVER_ERROR.getCode(), e.getMessage());
        }
    }

    @GetMapping("/getPackageByKey")
    @Operation(summary = "查询订阅套餐")
    public CommonResult<AiUserPackageResponse> getPackageByKey(@RequestParam(value = "configKey", required = false) String configKey,
                                                               @RequestHeader(value=HttpHeaders.AUTHORIZATION, required = false) String auth,
                                                               @RequestParam(value = "locale", required = false, defaultValue = BaseConstant.ZH) String locale) {
        try {
            if (StringUtils.isBlank(configKey)) {
                configKey = AiAppUserPackageService.PACKAGE_ALL_APPS;
            }

            AiUserPackageResponse res = aiAppUserPackageService.getUserPackage(auth, configKey, locale);

            return CommonResult.success(res);
        } catch (Exception e) {
            return CommonResult.error(INTERNAL_SERVER_ERROR.getCode(), e.getMessage());
        }
    }

    @GetMapping("/getPackageByDomain")
    @Operation(summary = "查询订阅套餐")
    public CommonResult<Map<String, AiUserPackageResponse>> getPackageByDomain(@RequestParam(value = "configKey") String configKey,
                                                                     @RequestHeader(value=HttpHeaders.AUTHORIZATION, required = false) String auth,
                                                                     @RequestParam(value = "locale", required = false, defaultValue = BaseConstant.ZH) String locale) {
        try {
            Map<String, AiUserPackageResponse> res = aiAppUserPackageService.getPackageByDomain(auth, configKey.replace("www.", BaseConstant.EMPTY_STR), locale);
            return CommonResult.success(res);
        } catch (Exception e) {
            return CommonResult.error(INTERNAL_SERVER_ERROR.getCode(), e.getMessage());
        }
    }

    @GetMapping("/mockAlipay")
    @Operation(summary = "mock支付宝")
    public CommonResult<String> mockAlipay() {
        try {
            return CommonResult.success("此处模拟扫描后调起支付宝支付，且支付成功");
        } catch (Exception e) {
            return CommonResult.error(INTERNAL_SERVER_ERROR.getCode(), e.getMessage());
        }
    }

    @GetMapping("/free-limit")
    @Operation(summary = "获取免费使用次数限制")
    public CommonResult<Integer> getFreeLimit() {
        int limit = aiAppUsageRecordsService.getFreeAppUsageLimit();
        return success(limit);
    }

    @PostMapping("/getAppByConfigKey")
    @Operation(summary = "获取活动的apps")
    public CommonResult<?> getAppByConfigKey(@RequestBody @Valid AppByConfigKeyReqVO param, @RequestHeader(value=HttpHeaders.AUTHORIZATION, required = false) String auth,
                                             @RequestParam(value = "locale", required = false, defaultValue = BaseConstant.ZH) String locale) {
        List<AiAppResponse> res = aiAppLangsService.getAppByConfigKey(param.getConfigKey(), auth, locale);

        return CommonResult.success(res);
    }

    @PostMapping("/getAppByDomain")
    @Operation(summary = "获取活动的apps")
    public CommonResult<?> getAppByDomain(@RequestBody @Valid AppByConfigKeyReqVO param, @RequestHeader(value=HttpHeaders.AUTHORIZATION, required = false) String auth,
                                             @RequestParam(value = "locale", required = false, defaultValue = BaseConstant.ZH) String locale) {
        List<AiAppResponse> res = aiAppLangsService.getAppByDomain(param.getConfigKey().replace("www.", BaseConstant.EMPTY_STR), auth, locale);

        return CommonResult.success(res);
    }

    @PostMapping("/saveCollect")
    @Operation(summary = "收集活动用户信息")
    public CommonResult<?> saveCollect(@RequestBody @Valid SaveActivityCollectReqVO param) {
        Boolean res = aiActivityCollectService.saveCollect(param);

        return CommonResult.success(res);
    }

    @GetMapping("/getConfigByKeyFromCache")
    @Operation(summary = "从缓存中获取配置（支持语言过滤）")
    public CommonResult<?> getConfigByKeyFromCache(@RequestParam(value = "configKey") String configKey,
                                                   @RequestParam(value = "locale", required = false, defaultValue = BaseConstant.ZH) String locale) {
        return CommonResult.success(yudaoSystemService.filterConfigByKeyFromCache(configKey, locale));
    }

    @GetMapping("/getPurchaseRecords")
    @Operation(summary = "生成随机字母数组并拼接订阅信息")
    public CommonResult<?> getPurchaseRecords() {
        return CommonResult.success(medsciUsersService.getPurchaseRecords());
    }

    @GetMapping("/getMessages")
    @Operation(summary = "根据dify会话id获取历史消息，主要用于分享/案例")
    public CommonResult<?> getMessages(@RequestParam(value = "encryptionId") String encryptionId) {
        try {
            JSONObject caseItem = yudaoSystemService.getCaseItem(encryptionId);
            JSONObject res = apiTokensService.getMessages(caseItem.getString("conversationId"));
            res.put("appNameEn", caseItem.getString("appNameEn"));
            res.put("title", caseItem.getString("title"));
            return CommonResult.success(res);
        } catch (Exception e) {
            e.printStackTrace();
            return CommonResult.error(INTERNAL_SERVER_ERROR.getCode(), e.getMessage());
        }
    }

    @GetMapping("/getSignedFileUrl")
    @Operation(summary = "根据dify附件id获取签名URL")
    public CommonResult<?> getSignedFileUrl(@RequestParam(value = "uploadFileId") String uploadFileId) {
        return CommonResult.success(apiTokensService.getSignedFileUrl(uploadFileId));
    }

}
