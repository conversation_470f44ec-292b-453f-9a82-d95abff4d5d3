package cn.iocoder.yudao.aiBase.dto.param;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.time.LocalDateTime;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AiFeeTypeParam extends PageParam {

    @Schema(description = "应用uuid")
    private String appUuid;

    @Schema(description = "应用语言")
    private String lang;

    @Schema(description = "显示名称，一般是订阅方式")
    private String displayName;

    @Schema(description = "订阅套餐，一般是appNameEn")
    private String packageKey;

    @Schema(description = "订阅方式")
    private String packageType;

    @Schema(description = "价格ID，币种是人民币的是支付宝场景值，美元的是stripe")
    private String priceId;

    @Schema(description = "1开启，0下架")
    private Integer online;

    @Schema(description = "支付宝订阅，stripe订阅，按次收费")
    private String priceType;

    @Schema(description = "备注")
    private String remark;
}
