package cn.iocoder.yudao.aiBase.mapper;

import cn.iocoder.yudao.aiBase.config.BaseConstant;
import cn.iocoder.yudao.aiBase.dto.param.UserPackageParam;
import cn.iocoder.yudao.aiBase.entity.AiAppUserPackage;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;


/**
 * 操作记录
 */
@Mapper
public interface AiAppUserPackageMapper extends BaseMapperX<AiAppUserPackage> {

    default PageResult<AiAppUserPackage> selectPage(UserPackageParam reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<AiAppUserPackage>()
            .eqIfPresent(AiAppUserPackage::getSocialUserId, reqVO.getSocialUserId())
            .eqIfPresent(AiAppUserPackage::getSocialType, reqVO.getSocialType())
            .eqIfPresent(AiAppUserPackage::getPackageKey, reqVO.getPackageKey())
            .eqIfPresent(AiAppUserPackage::getLang, reqVO.getLang())
            .eqIfPresent(AiAppUserPackage::getPackageType, reqVO.getPackageType())
            .eqIfPresent(AiAppUserPackage::getCheckoutSessionId, reqVO.getCheckoutSessionId())
            .eqIfPresent(AiAppUserPackage::getSubStatus, reqVO.getSubStatus())
            .betweenIfPresent(AiAppUserPackage::getExpireAt, reqVO.getExpiredAt())
            .eq(AiAppUserPackage::getDeleted, BaseConstant.ZERO)
            .orderByDesc(AiAppUserPackage::getId));
    }

    default List<AiAppUserPackage> selectList(UserPackageParam reqVO) {
        return selectList(new LambdaQueryWrapperX<AiAppUserPackage>()
            .eqIfPresent(AiAppUserPackage::getSocialUserId, reqVO.getSocialUserId())
            .eqIfPresent(AiAppUserPackage::getSocialType, reqVO.getSocialType())
            .eqIfPresent(AiAppUserPackage::getLang, reqVO.getLang())
            .eqIfPresent(AiAppUserPackage::getPackageKey, reqVO.getPackageKey())
            .eqIfPresent(AiAppUserPackage::getPackageType, reqVO.getPackageType())
            .eqIfPresent(AiAppUserPackage::getCheckoutSessionId, reqVO.getCheckoutSessionId())
            .eqIfPresent(AiAppUserPackage::getSubStatus, reqVO.getSubStatus())
            .betweenIfPresent(AiAppUserPackage::getExpireAt, reqVO.getExpiredAt())
            .eq(AiAppUserPackage::getDeleted, BaseConstant.ZERO)
            .orderByDesc(AiAppUserPackage::getId));
    }

}
