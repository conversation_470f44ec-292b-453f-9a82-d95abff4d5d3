package cn.iocoder.yudao.aiBase.service.impl;

import cn.hutool.core.util.IdUtil;
import cn.iocoder.yudao.aiBase.config.BaseConstant;
import cn.iocoder.yudao.aiBase.config.DBConstant;
import cn.iocoder.yudao.aiBase.config.DifyBaseConfig;
import cn.iocoder.yudao.aiBase.config.ErrorCodeConstants;
import cn.iocoder.yudao.aiBase.dto.alipay.BusinessQueryOrderResponse;
import cn.iocoder.yudao.aiBase.dto.alipay.PayNotifyDto;
import cn.iocoder.yudao.aiBase.dto.alipay.PayStatus;
import cn.iocoder.yudao.aiBase.dto.param.AiAppParam;
import cn.iocoder.yudao.aiBase.dto.param.AiPageParam;
import cn.iocoder.yudao.aiBase.dto.param.MsUserParam;
import cn.iocoder.yudao.aiBase.dto.param.UserPackageParam;
import cn.iocoder.yudao.aiBase.dto.request.*;
import cn.iocoder.yudao.aiBase.dto.response.*;
import cn.iocoder.yudao.aiBase.entity.*;
import cn.iocoder.yudao.aiBase.mapper.AiAppLangsMapper;
import cn.iocoder.yudao.aiBase.mq.SyncUserAppProducer;
import cn.iocoder.yudao.aiBase.mq.SyncUserAppMsg;
import cn.iocoder.yudao.aiBase.service.*;
import cn.iocoder.yudao.aiBase.util.CommonUtil;
import cn.iocoder.yudao.aiBase.util.MedsciSubUtil;
import cn.iocoder.yudao.aiBase.util.MsWechatUtil;
import cn.iocoder.yudao.aiBase.util.StripeUtil;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.json.JsonUtils;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.framework.common.util.servlet.ServletUtils;
import cn.iocoder.yudao.framework.security.core.util.SecurityFrameworkUtils;
import cn.iocoder.yudao.module.infra.controller.admin.config.vo.ConfigSaveReqVO;
import cn.iocoder.yudao.module.infra.dal.dataobject.config.ConfigDO;
import cn.iocoder.yudao.module.system.api.oauth2.dto.OAuth2AccessTokenCheckRespDTO;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.stripe.model.*;
import com.stripe.model.checkout.Session;
import com.stripe.param.PriceCreateParams;
import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.*;
import java.util.stream.Collectors;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;

@Slf4j
@Service
@DS(DBConstant.AiBase)
public class AiAppLangsServiceImpl extends ServiceImpl<AiAppLangsMapper, AiAppLangs> implements AiAppLangsService {

    public static final String ONLINE = "上架";
    public static final String OFFLINE = "下架";

    @Autowired
    private MedsciUsersService medsciUsersService;

    @Autowired
    private AiAppUsersService aiAppUsersService;

    @Autowired
    private AiSubscriptionLogService aiSubscriptionLogService;

    @Autowired
    private AiStripeEventLogService aiStripeEventLogService;

    @Autowired
    private AiSubOrdersService aiSubOrdersService;

    @Autowired
    private AiAppUserPackageService aiAppUserPackageService;

    @Autowired
    private YudaoSystemService yudaoSystemService;

    @Autowired
    private RedisManage redisManage;

    @Autowired
    private OauthService oauthService;

    @Autowired
    private MockMedsciSubService mockMedsciSubService;

    @Value("${stripe-config.success-url}")
    private String successUrl;

    @Value("${stripe-config.cancel-url}")
    private String cancelUrl;

    @Value("${spring.profiles.active}")
    private String active;

    @Autowired
    private DifyBaseConfig difyBaseConfig;

    @Autowired
    private SyncUserAppProducer syncAppUserProducer;

    @Override
    public PageResult<AiAppLangs> selectPage(AiPageParam reqVO) {
        return baseMapper.selectPage(reqVO);
    }

    @Override
    public List<AiAppLangs> selectList(AiAppParam reqVO) {
        return baseMapper.selectList(reqVO);
    }

    @Override
    public List<AiAppLangs> selectListOnline(AiAppParam reqVO) {
        reqVO.setAppStatus(ONLINE);
        return selectList(reqVO);
    }

    @Override
    public Map<String, AiAppLangs> selectMap(AiAppParam reqVO) {
        return selectList(reqVO).stream().collect(Collectors.toMap(AiAppLangs::getAppUuid, item -> item));
    }

    @Override
    public List<AiAppLangs> getSiteMapList() {
        // 示例实现（请根据实际业务逻辑替换）
        return baseMapper.getSiteMapList();
    }

    @Override
    public AiAppLangs getByUuid(String uuid) {
        if (StringUtils.isBlank(uuid)) {
            return null;
        }

        uuid = uuid.replace("-", BaseConstant.EMPTY_STR);
        List<AiAppLangs> list = selectList(AiAppParam.builder().appUuid(uuid).build());
        return list.isEmpty() ? null : list.get(BaseConstant.ZERO);
    }

    /**
     * 2025-07-21 作废
     * @param nameEn 应用英文名
     * @return
     */
    @Override
    public AiAppLangs getByNameEn(String nameEn) {
        // 之前nameEn是 大驼峰格式，如 MedWrite,现在改为了 med-write,需要兼容老的地址访问
        nameEn = CommonUtil.camelToKebab(nameEn);
        List<AiAppLangs> list = selectList(AiAppParam.builder().appNameEn(nameEn).build());
        return list.isEmpty() ? null : list.get(BaseConstant.ZERO);
    }

    @Override
    public AiAppLangs getByNameEn(String nameEn, String locale) {
        nameEn = CommonUtil.camelToKebab(nameEn);
        List<AiAppLangs> list = selectList(AiAppParam.builder().appNameEn(nameEn).build());
        if (list.size() > BaseConstant.ONE) {
            // 如果name不是唯一，则再根据语言筛选
            return list.stream().filter(app -> app.getLang().equals(locale)).findFirst().orElse(null);
        }
        return list.isEmpty() ? null : list.get(BaseConstant.ZERO);
    }

    @Override
    public List<AiAppResponse> getAppList(AiAppParam reqVO, String auth) {
        OAuth2AccessTokenCheckRespDTO authUser = oauthService.checkAccessToken(auth);
        if (authUser != null) {
            if (BaseConstant.ONE.equals(reqVO.getIsMine())) {
                // 我的：显示全部，显示外部应用、内部应用、活动应用
            } else {
                // 不是 我的：显示外部应用和内部应用，不显示活动应用
                if (BaseConstant.ONE.toString().equals(medsciUsersService.getIsInternalUser(authUser.getUserId(), authUser.getUserType()))) {
                    // 内部用户
                    reqVO.setIsInternalUsers(Arrays.asList(BaseConstant.ZERO, BaseConstant.ONE));
                } else {
                    // 外部用户
                    reqVO.setIsInternalUsers(Arrays.asList(BaseConstant.ZERO));
                }
            }
        } else {
            // 未登录
            if (BaseConstant.ONE.equals(reqVO.getIsMine())) {
                throw exception(ErrorCodeConstants.UNAUTHORIZED);
            } else {
                reqVO.setIsInternalUsers(Arrays.asList(BaseConstant.ZERO));
            }
        }
        return filterAppList(reqVO, auth);
    }

    /**
     * 查询过滤
     * @param reqVO
     * @param auth
     * @return
     */
    private List<AiAppResponse> filterAppList(AiAppParam reqVO, String auth) {
        OAuth2AccessTokenCheckRespDTO authUser = oauthService.checkAccessToken(auth);

        List<AiAppResponse> res = new ArrayList<>();
        List<AiAppLangs> list = selectListOnline(reqVO);
        for (AiAppLangs item : list) {
            if (StringUtils.isBlank(item.getDifyAppUuid())) {
                // 没有配置dify的不显示
                continue;
            }
            if (difyBaseConfig.getHideAppIds().contains(item.getDifyAppUuid())) {
                // 主站文章的应用不显示在首页
                continue;
            }

            AiAppResponse record = toBean(item);
            if (record == null) {
                continue;
            }

            if (authUser != null) {
                record.setAppUser(getAppUser(authUser.getUserType(), authUser.getUserId(), record.getAppUuid()));
            }

            if (BaseConstant.ONE.equals(reqVO.getIsMine())) {
                if (record.getAppUser()!=null) {
                    res.add(record);
                }
            } else {
                res.add(record);
            }
        }

        return res;
    }

    private AiAppUserResponse getAppUser(Integer socialType, Long socialUserId, String appUuid) {
        try {
            String value = redisManage.getUserAppUser(socialType, socialUserId, appUuid);
            if (StringUtils.isNotBlank(value)) {
                return JsonUtils.parseObject(value, AiAppUserResponse.class);
            }

            AiAppUsers appUser = aiAppUsersService.getAppUser(socialUserId, socialType, appUuid);
            if (appUser == null) {
                return null;
            }

            AiAppUserResponse response = BeanUtils.toBean(appUser, AiAppUserResponse.class, item -> {
                if (appUser.getExpireAt() != null) {
                    item.setExpireAt(appUser.getExpireAt().format(CommonUtil.DateTimeFormat1));
                }
            });

            value = JsonUtils.toJsonString(response);
            redisManage.setUserAppUser(socialType, socialUserId, appUuid, value);

            return response;

        } catch (Exception e) {
            return null;
        }
    }

    @Override
    public List<AiAppResponse> getAppByConfigKey(String configKey, String auth, String locale) {
        List<String> nameEns = new ArrayList<>();
        if (StringUtils.isNotBlank(configKey)) {
            // 查找配置的活动apps
            ConfigDO configDO = yudaoSystemService.getConfigByKey(configKey);
            if (configDO!=null) {
                nameEns = Arrays.stream(configDO.getValue().split(BaseConstant.COMMA_STR)).toList();
            }
        }

        if (nameEns.isEmpty()) {
            return new ArrayList<>();
        }
        AiAppParam reqVO = AiAppParam.builder().appNameEns(nameEns).lang(locale).isAscId(true).build();
        return filterAppList(reqVO, auth);
    }

    @Override
    public List<AiAppResponse> getAppByDomain(String configKey, String auth, String locale) {
        List<String> nameEns = new ArrayList<>();
        if (StringUtils.isNotBlank(configKey)) {
            // 查找配置的活动apps
            String apps = yudaoSystemService.getConfigByKeyFromCache("apps", configKey+".apps");
            if (apps != null) {
                nameEns.addAll(JSONObject.parseArray(apps, String.class));
            }
        }

        if (nameEns.isEmpty()) {
            return new ArrayList<>();
        }
        AiAppParam reqVO = AiAppParam.builder().appNameEns(nameEns).lang(locale).isAscId(true).build();
        List<AiAppResponse> res = filterAppList(reqVO, auth);

        Long userId = SecurityFrameworkUtils.getLoginUserId();
        if (userId != null) {
            String testApps = yudaoSystemService.getConfigByKeyFromCache("test_apps_users");
            nameEns = JSONObject.parseObject(testApps).getJSONArray("apps").toJavaList(String.class);
            List<Long>userIds = JSONObject.parseObject(testApps).getJSONArray("users").toJavaList(Long.class);
            if (StringUtils.isNotBlank(testApps) && userIds.contains(userId) && !nameEns.isEmpty()) {
                reqVO = AiAppParam.builder().appNameEns(nameEns).lang(locale).isAscId(true).build();
                res.addAll(filterAppList(reqVO, auth));
            }
        }

        return res;
    }

    @Override
    public AiAppResponse toBean(AiAppLangs item) {
        AiAppResponse record = BeanUtils.toBean(item, AiAppResponse.class, item1 -> {
            List<FeeTypeVo>feeTypes = new ArrayList<>();
            if (ZH_CN.equals(item1.getAppLang())) {
                feeTypes = aiAppUserPackageService.getFeeTypes(item.getAppNameEn());
                if (feeTypes==null || feeTypes.isEmpty()) {
                    // 没有取到则取默认的
                    feeTypes = aiAppUserPackageService.getFeeTypes(AiAppUserPackageService.PACKAGE_ALL_APPS);
                }
            } else {
                feeTypes = getFeeTypes(item);
            }

            item1.setFeeTypes(feeTypes);
        });
        return record;
    }

    /**
     * 获取 非中文应用 的收费类型
     * @param item
     * @return
     */
    private List<FeeTypeVo> getFeeTypes(AiAppLangs item) {
        try {
            String key = item.getLang()+BaseConstant.COLON_STR+item.getAppNameEn();
            String value = redisManage.getFeeTypes(key);
            if (value == null) {
                List<FeeTypeVo> feeTypes = new ArrayList<>();
                if (BaseConstant.ONE.equals(item.getFeeType()) &&
                        BigDecimal.ZERO.compareTo(item.getFeePrice()) == 0) {
                    // 连续包月的价格为0，则统一是 免费
                    feeTypes.add(toFeeType(item.getFeeType(), FeeTypeVo.FREE_TYPE, BaseConstant.ONE, FeeTypeVo.FREE_PRICE_ID, item.getFeePrice(), item.getCoinType()));
                } else {
                    feeTypes.add(toFeeType(item.getFeeType(), FeeTypeVo.MONTH_TYPE, BaseConstant.ONE, item.getFeePriceId(), item.getFeePrice(), item.getCoinType()));
                }
                feeTypes.add(toFeeType(item.getFeeType2(), FeeTypeVo.QUARTER_TYPE, BaseConstant.THREE, item.getFeePriceId2(), item.getFeePrice2(), item.getCoinType()));
                feeTypes.add(toFeeType(item.getFeeType3(), FeeTypeVo.YEAR_TYPE, BaseConstant.TWELVE, item.getFeePriceId3(), item.getFeePrice3(), item.getCoinType()));
                value = JsonUtils.toJsonString(feeTypes);
                redisManage.setFeeTypes(key, value);
            }

            return JSONArray.parseArray(value).toJavaList(FeeTypeVo.class)
                    .stream().filter(feeType -> BaseConstant.ONE.equals(feeType.getOnline()) &&
                            (StringUtils.isNotBlank(feeType.getPriceId()) || feeType.getFeePrice().compareTo(BigDecimal.ZERO)==0))
                    .map(feeTypeVo -> feeTypeVo.setPackageKey(BaseConstant.PLACE_HOLDER)) // 非中文的，设置为占位符
                    .toList();
        } catch (Exception e) {
            log.error("[getFeeTypes][获取失败]", e);
            return new ArrayList<>();
        }
    }

    /**
     * 转换收费类型
     * @param online
     * @param feeType
     * @param monthNum
     * @param priceId
     * @param feePrice
     * @param coinType
     * @return
     */
    private FeeTypeVo toFeeType(Integer online, String feeType, Integer monthNum, String priceId, BigDecimal feePrice, String coinType) {
        FeeTypeVo feeTypeVo = new FeeTypeVo();
        feeTypeVo.setOnline(online);
        feeTypeVo.setType(feeType);
        feeTypeVo.setMonthNum(monthNum);
        feeTypeVo.setPriceId(priceId);
        feeTypeVo.setFeePrice(feePrice);
        feeTypeVo.setCoinType(coinType);
        return feeTypeVo;
    }

    @Override
    public Integer getAppClickNum(String appUuid, String openid) {
        Integer num = BaseConstant.ZERO;
        AiAppLangs appLangs = getByUuid(appUuid);
        if (appLangs != null) {
            num = appLangs.getClickNum() + BaseConstant.ONE;

            Integer id = appLangs.getId();
            appLangs = new AiAppLangs();
            appLangs.setId(id);
            appLangs.setClickNum(num);
            baseMapper.updateById(appLangs);
        }
        return num;
    }

    @Override
    public Integer updateUseNum(String appUuid) {
        AiAppLangs appLang = getByUuid(appUuid);

        AiAppLangs record = new AiAppLangs();
        record.setId(appLang.getId());
        record.setUseNum(appLang.getUseNum() + BaseConstant.ONE);
        baseMapper.updateById(record);
        return record.getUseNum();
    }

    /**
     * 非中文应用初始化stripe 产品
     * @param oldApp
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void createProduct(AppLangReqVO oldApp) {
        AiAppLangs appLangs = getByUuid(oldApp.getAppUuid());
        if (appLangs == null) {
            return;
        }

        if (!appLangs.getAppNameEn().equals(CommonUtil.camelToKebab(appLangs.getAppNameEn()))) {
            appLangs.setAppNameEn(CommonUtil.camelToKebab(appLangs.getAppNameEn()));
            baseMapper.updateById(appLangs);
        }

        if (!getLanguages(appLangs.getAppLang()).equals(appLangs.getLang())) {
            appLangs.setLang(getLanguages(appLangs.getAppLang()).replace(BaseConstant.SLASH_STR, BaseConstant.EMPTY_STR));
            baseMapper.updateById(appLangs);
        }

        List<AiAppLangs> list = selectList(AiAppParam.builder().appNameEn(appLangs.getAppNameEn()).lang(appLangs.getLang()).build());
        if (list.size() > BaseConstant.ONE) {
            log.error("[createProduct][应用名称重复 则下架应用]：{}", appLangs.getAppUuid());
            appLangs.setAppStatus(OFFLINE);
            baseMapper.updateById(appLangs);
        }

        redisManage.updateAppNameEn(appLangs.getAppUuid(), appLangs.getAppNameEn());

        if (!ZH_CN.equals(appLangs.getAppLang())) {
            processNonChineseApp(appLangs);
        } else {
            asyncAppUser(appLangs);
        }
    }

    /**
     * 异步处理非中文应用的Stripe产品创建
     * @param appLangs 应用语言信息
     */
    @Async
    @DS(DBConstant.AiBase)
    private void processNonChineseApp(AiAppLangs appLangs) {
        boolean hasZeroPriceForSeasonalOrYearly = false;
        AiAppLangs record = new AiAppLangs();
        record.setId(appLangs.getId());

        // 确保产品存在
        ensureProductExists(record, appLangs.getAppName(), appLangs.getAppDescription());
        if (StringUtils.isBlank(record.getProductId())) {
            return;
        }

        if (BaseConstant.ONE.equals(appLangs.getFeeType())) {
            record.setFeePriceId(getPriceId(record.getProductId(), appLangs.getFeePriceId(), appLangs.getFeePrice(), 1L));
        }

        if (BaseConstant.ONE.equals(appLangs.getFeeType2())) {
            record.setFeePriceId2(getPriceId(record.getProductId(), appLangs.getFeePriceId2(), appLangs.getFeePrice2(), 3L));
            if (appLangs.getFeePrice2() != null && BigDecimal.ZERO.compareTo(appLangs.getFeePrice2()) == 0) {
                hasZeroPriceForSeasonalOrYearly = true;
            }
        }

        if (BaseConstant.ONE.equals(appLangs.getFeeType3())) {
            record.setFeePriceId3(getPriceId(record.getProductId(), appLangs.getFeePriceId3(), appLangs.getFeePrice3(), 12L));
            if (appLangs.getFeePrice3() != null && BigDecimal.ZERO.compareTo(appLangs.getFeePrice3()) == 0) {
                hasZeroPriceForSeasonalOrYearly = true;
            }
        }

        // 如果季度或年度价格为0，则下架应用
        if (hasZeroPriceForSeasonalOrYearly) {
            record.setAppStatus(OFFLINE);
            log.error("[createProduct][季年的价格为0 则下架应用]：{}", appLangs.getAppUuid());
        }

        baseMapper.updateById(record);
        // 更新价格缓存
        String key = appLangs.getLang()+BaseConstant.COLON_STR+appLangs.getAppNameEn();
        redisManage.deleteFeeTypes(key);
    }

    /**
     * 确保Stripe产品存在，如果不存在则创建
     * @param record
     * @param appName
     * @param appDesc
     */
    private void ensureProductExists(AiAppLangs record, String appName, String appDesc) {
        String productId = record.getProductId();
        appDesc = StringUtils.isBlank(appDesc) ? appName : appDesc;

        if (StringUtils.isBlank(productId)) {
            // 产品ID为空，创建新产品
            Product product = StripeUtil.createProduct(active, appName, appDesc);
            if (product != null) {
                productId = product.getId();
                record.setProductId(productId);
            }
        } else {
            // 产品ID存在，验证产品是否存在
            Product product = StripeUtil.getProduct(active, productId);
            if (product == null) {
                // 产品不存在，重新创建
                product = StripeUtil.createProduct(active, appName, appDesc);
                if (product != null) {
                    productId = product.getId();
                    record.setProductId(productId);
                }
            }
        }
    }

    /**
     * 获取或创建价格ID
     * @param productId Stripe产品ID
     * @param priceId 现有价格ID
     * @param feePrice 费用价格
     * @param count 月数（1=月，3=季，12=年）
     * @return 价格ID
     */
    private String getPriceId(String productId, String priceId, BigDecimal feePrice, Long count) {
        // 验证费用价格
        if (feePrice == null || BigDecimal.ZERO.compareTo(feePrice) >= 0) {
            return null;
        }

        // 创建或获取价格（Stripe金额以分为单位）
        Price newPrice = StripeUtil.getPriceOrCreate(
                active, productId, feePrice.multiply(BigDecimal.valueOf(100L)), PriceCreateParams.Recurring.Interval.MONTH, count
        );

        // 如果已有价格ID，验证其有效性
        if (StringUtils.isNotBlank(priceId) && newPrice != null && !priceId.equals(newPrice.getId())) {
            return newPrice.getId();
        }

        return priceId;
    }

    /**
     * 创建新 app-user关系
     * @param appLangs
     */
    private void asyncAppUser(AiAppLangs appLangs) {
        if (BaseConstant.TWO.equals(appLangs.getIsInternalUser()) || !ONLINE.equals(appLangs.getAppStatus())) {
            log.info("保存应用，更新app-user关系：活动应用不自动创建关系，未上架应用不自动创建关系");
            return;
        }

        // 异步
//        CompletableFuture.runAsync(() -> {
            LocalDateTime now = LocalDateTime.now();
            if (BaseConstant.ONE.equals(appLangs.getIsInternalUser())) {
                List<MedsciUsers> users = medsciUsersService.selectList(MsUserParam.builder().isInternalUser(BaseConstant.ONE).build());
                for (MedsciUsers user : users) {
                    AiAppUserPackage userPackage = aiAppUserPackageService.getSubOrCancel(user.getSocialUserId(), user.getSocialType(), AiAppUserPackageService.PACKAGE_ALL_APPS);
                    if (userPackage != null) {
                        AiAppUsers appUser = aiAppUsersService.getOrCreateAppUser(user.getSocialUserId(), user.getSocialType(), BaseConstant.EMPTY_STR, appLangs.getAppUuid());
                        appUser.setStartAt(userPackage.getStartAt());
                        appUser.setExpireAt(userPackage.getExpireAt());
                        appUser.setUpdatedAt(now);
                        aiAppUsersService.updateById(appUser);
                        log.info("保存内部应用，更新app-user关系：{}-{}", appLangs.getAppUuid(), user.getSocialUserId());
                    }
                }
            } else {
                try {
                    List<AiAppLangs> list = selectList(AiAppParam.builder().appLang(ZH_CN).appStatus(ONLINE).isInternalUsers(Arrays.asList(BaseConstant.ZERO)).lastLimit(BaseConstant.TWO).build());
                    if (list.size() > BaseConstant.ONE) {
                        // 取倒数第二个应用（排除刚刚新建的应用）
                        AiAppLangs appLangs1 = list.get(BaseConstant.ONE);

                        // 查询所有的订阅中 和 退订中 的套餐用户
                        UserPackageParam reqVO = UserPackageParam.builder().packageKey(AiAppUserPackageService.PACKAGE_ALL_APPS)
                                .subStatus(BaseConstant.ONE).build();
                        List<AiAppUserPackage> userPackages = aiAppUserPackageService.selectList(reqVO);
                        reqVO.setSubStatus(BaseConstant.THREE);
                        userPackages.addAll(aiAppUserPackageService.selectList(reqVO));

                        log.info("保存应用，更新app-user关系：开始");
                        // todo
                        for (AiAppUserPackage userPackage : userPackages) {
                            syncAppUserProducer.send(SyncUserAppMsg.builder().socialUserId(userPackage.getSocialUserId())
                                    .socialType(userPackage.getSocialType()).appUuid(appLangs.getAppUuid()).appUuid1(appLangs1.getAppUuid()).build());
                        }
                    }
                } catch (Exception e) {
                    log.error("保存应用，更新app-user关系：{}", e.getMessage());
                }
            }
//        });
    }

    @Override
    public void handleSyncUserApp(SyncUserAppMsg msg) {
        LocalDateTime now = LocalDateTime.now();
        AiAppUsers appUser1 = aiAppUsersService.getAppUser(msg.getSocialUserId(), msg.getSocialType(), msg.getAppUuid1());
        AiAppUsers appUser = aiAppUsersService.getAppUser(msg.getSocialUserId(), msg.getSocialType(), msg.getAppUuid());
        if (appUser1!=null && appUser==null) {
            appUser1.setId(null);
            appUser1.setAppUuid(msg.getAppUuid());
            appUser1.setCreatedAt(now);
            appUser1.setUpdatedAt(now);
            if (now.isBefore(appUser1.getExpireAt())) {
                appUser1.setStatus(BaseConstant.ONE);
            }
            aiAppUsersService.save(appUser1);
            log.info("保存应用，更新app-user关系：{}-{}", msg.getAppUuid(), appUser1.getSocialUserId());
        }
    }

    /**
     * 初始化stripe产品
     * @param appUuid
     */
    @Override
    public void initProject(String appUuid) {
        List<AiAppLangs> list = selectList(AiAppParam.builder().appUuid(appUuid).appStatus(ONLINE).build());
        for (AiAppLangs appLang : list) {
            AppLangReqVO oldApp = new AppLangReqVO();
            oldApp.setAppUuid(appLang.getAppUuid());
            oldApp.setFeePrice(BigDecimal.ZERO);
            oldApp.setFeePrice2(BigDecimal.ZERO);
            oldApp.setFeePrice3(BigDecimal.ZERO);
            createProduct(oldApp);
        }
    }

    /**
     * 创建订阅链接
     *
     * @param auth
     * @param reqVO
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public String createSubscription(String auth, CreateSubReqVO reqVO) {
        OAuth2AccessTokenCheckRespDTO authUser = oauthService.checkAccessTokenOrError(auth);
        // 1. 验证用户和请求参数
        validateSubscriptionRequest(reqVO);
        MedsciUsers user = medsciUsersService.getUserByAuthUser(authUser);
        
        // 2. 根据请求类型处理订阅
        return isPackageSubscription(reqVO) 
                ? handlePackageSubscription(user, reqVO)
                : handleAppSubscription(user, reqVO);
    }
    
    /**
     * 验证订阅请求参数
     */
    private void validateSubscriptionRequest(CreateSubReqVO reqVO) {
        if (StringUtils.isBlank(reqVO.getAppUuid()) && (StringUtils.isBlank(reqVO.getPackageKey()) || StringUtils.isBlank(reqVO.getPackageType()))) {
            throw exception(ErrorCodeConstants.ERROR_5050);
        }
    }
    
    /**
     * 判断是否为套餐订阅
     */
    private boolean isPackageSubscription(CreateSubReqVO reqVO) {
        return !StringUtils.isBlank(reqVO.getPackageKey()) && !StringUtils.isBlank(reqVO.getPackageType()) && !BaseConstant.PLACE_HOLDER.equals(reqVO.getPackageKey());
    }
    
    /**
     * 处理中文套餐订阅
     */
    private String handlePackageSubscription(MedsciUsers user, CreateSubReqVO reqVO) {
        // 1. 获取套餐费用类型
        FeeTypeVo feeType = aiAppUserPackageService.getFeeType(reqVO.getPackageKey(), reqVO.getPackageType());
        if (feeType == null) {
            throw exception(ErrorCodeConstants.ERROR_5027);
        }

        // 2.受邀用户
        JSONArray freeUsers = yudaoSystemService.getFreeUsers(reqVO.getPackageKey());
        if (freeUsers != null) {
            if (!freeUsers.contains(user.getSocialType()+BaseConstant.COLON_STR+ user.getSocialUserId())) {
                throw exception(ErrorCodeConstants.ERROR_5054);
            }
            if (!yudaoSystemService.getIsFreeTime(reqVO.getPackageKey())) {
                throw exception(ErrorCodeConstants.ERROR_5055);
            }
        }
        
        // 3. 获取或创建用户套餐
        AiAppUserPackage userPackage = aiAppUserPackageService.getOrCreateUserPackage(
                user.getSocialUserId(), user.getSocialType(), reqVO.getPackageKey(), feeType.getType());
        
        // 4. 检查订阅状态
        checkPackageSubscriptionStatus(user, userPackage);
        
        // 5. 创建支付宝订阅
        return createAliSub(user, reqVO, feeType, userPackage.getId());
    }
    
    /**
     * 检查套餐订阅状态
     */
    private void checkPackageSubscriptionStatus(MedsciUsers user, AiAppUserPackage userPackage) {
        if (userPackage.getSubStatus().equals(BaseConstant.ONE)) {
            throw exception(ErrorCodeConstants.ERROR_5028);
        }
        if (userPackage.getSubStatus().equals(BaseConstant.THREE)) {
            throw exception(ErrorCodeConstants.ERROR_5051);
        }
        
        // 当前点击的套餐没有订阅，则检查是否有其他非免费订阅，同一个key只有一个订阅状态
        AiAppUserPackage otherPackage = aiAppUserPackageService.getUserPackageOnSub(user.getSocialUserId(), user.getSocialType(), userPackage.getPackageKey());
        if (otherPackage != null && !otherPackage.getPackageType().equals(FeeTypeVo.FREE_TYPE)) {
            throw exception(ErrorCodeConstants.ERROR_5028);
        }
        
        // 检查是否有取消中的订阅，同一个key只有一个退订状态
        otherPackage = aiAppUserPackageService.getUserPackageOnCancel(user.getSocialUserId(), user.getSocialType(), userPackage.getPackageKey());
        if (otherPackage != null) {
            throw exception(ErrorCodeConstants.ERROR_5051);
        }
    }
    
    /**
     * 处理非中文应用订阅
     */
    private String handleAppSubscription(MedsciUsers user, CreateSubReqVO reqVO) {
        // 1. 获取应用信息
        AiAppLangs appLangs = getByUuid(reqVO.getAppUuid());
        if (appLangs == null || !ONLINE.equals(appLangs.getAppStatus()) || ZH_CN.equals(appLangs.getAppLang())) {
            throw exception(ErrorCodeConstants.ERROR_5027);
        }
        
        // 2. 获取费用类型
        AiAppResponse appLang = toBean(appLangs);
        FeeTypeVo feeType = getFeeTypeForApp(appLang, reqVO);
        
        // 3. 检查是否为免费应用
        String freeSubscriptionResult = checkFreeAppSubscription(user, reqVO, appLangs, feeType);
        if (freeSubscriptionResult != null) {
            return freeSubscriptionResult;
        }
        
        // 4. 检查用户订阅状态
        AiAppUsers appUser = aiAppUsersService.getAppUser(
                user.getSocialUserId(), user.getSocialType(), reqVO.getAppUuid());
        if (appUser != null && BaseConstant.ONE.equals(appUser.getStatus())) {
            throw exception(ErrorCodeConstants.ERROR_5028);
        }
        
        // 5. 验证价格ID
        if (StringUtils.isBlank(feeType.getPriceId())) {
            throw exception(ErrorCodeConstants.ERROR_5044);
        }
        
        // 6. 创建Stripe订阅
        return createStripeSub(user, reqVO, appUser, feeType.getFeePrice());
    }
    
    /**
     * 获取应用费用类型
     */
    private FeeTypeVo getFeeTypeForApp(AiAppResponse appLang, CreateSubReqVO reqVO) {
        if (StringUtils.isNotBlank(reqVO.getPriceId())) {
            return appLang.getFeeTypes().stream()
                    .filter(item -> item.getPriceId().equals(reqVO.getPriceId()))
                    .findFirst()
                    .orElse(null);
        } else {
            return appLang.getFeeTypes().stream()
                    .filter(item -> item.getMonthNum().equals(reqVO.getMonthNum()))
                    .findFirst()
                    .orElse(null);
        }
    }
    
    /**
     * 检查是否为免费应用订阅
     * @return 如果是免费应用返回订阅URL，否则返回null
     */
    private String checkFreeAppSubscription(MedsciUsers user, CreateSubReqVO reqVO, 
                                           AiAppLangs appLangs, FeeTypeVo feeType) {
        // 检查是否为免费应用
        if (feeType == null) {
            throw exception(ErrorCodeConstants.ERROR_5027);
        }
        
        // 月费用是开启的，且金额是0，则此应用是 免费
        if (FeeTypeVo.FREE_TYPE.equals(feeType.getType()) &&
                BigDecimal.ZERO.compareTo(appLangs.getFeePrice()) == 0) {
            return createAliSub(user, reqVO, feeType, BaseConstant.ZERO);
        }
        
        return null;
    }

    /**
     * 异步创建自动订阅
     * @param socialUserId
     * @param openid
     * @param nameEn
     * @param locale
     */
    @Async
    @DS(DBConstant.AiBase)
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void createAutoSubscription(Long socialUserId, String openid, String nameEn, String locale) {

        MedsciUsers user = medsciUsersService.getBySocialUserIdAndOpenid(socialUserId, openid);


        AiAppLangs appLangs = getByNameEn(nameEn, locale);
        if (appLangs == null || !ONLINE.equals(appLangs.getAppStatus())) {
            throw exception(ErrorCodeConstants.ERROR_5027);
        }

        // 先检查用户订阅
        AiAppUsers appUser = aiAppUsersService.getAppUser(user.getSocialUserId(), user.getSocialType(), appLangs.getAppUuid());
        if (appUser != null && BaseConstant.ONE.equals(appUser.getStatus())) {
            log.info("应用已订阅：{},{}", appLangs.getAppUuid(), user.getSocialUserId());
            return;
        }

        // 免费订阅的
        // 先创建关联
        LocalDateTime start = LocalDateTime.now().withDayOfMonth(BaseConstant.ONE).with(LocalTime.MIN);
        aiAppUsersService.updateSub(user.getSocialUserId(), user.getSocialType(), appLangs.getAppUuid(), start, start.plusMonths(BaseConstant.ONE));
        // 再创建订阅
        FeeTypeVo feeType = aiAppUserPackageService.getFeeType(AiAppUserPackageService.PACKAGE_ALL_APPS, FeeTypeVo.FREE_TYPE);
        AiAppUserPackage userPackage = aiAppUserPackageService.getOrCreateUserPackage(user.getSocialUserId(), user.getSocialType(), AiAppUserPackageService.PACKAGE_ALL_APPS, FeeTypeVo.FREE_TYPE);
        if (!BaseConstant.ONE.equals(userPackage.getSubStatus())) {
            // 不是订阅状态，则订阅
            createAliSub(user, CreateSubReqVO.builder().appUuid(appLangs.getAppUuid()).build(), feeType, userPackage.getId());
        }

    }

    private String createStripeSub(MedsciUsers user, CreateSubReqVO reqVO, AiAppUsers appUser, BigDecimal payAmount) {

        if (StringUtils.isBlank(user.getStripeCustomerId())) {
            throw exception(ErrorCodeConstants.ERROR_5029);
        }

        // 在检查是否有有效订阅链接
        AiSubscriptionLog subLog = aiSubscriptionLogService.getLastValid(user.getSocialUserId(), user.getSocialType(), reqVO.getAppUuid(), reqVO.getPriceId());
        Session session = null;
        if (subLog != null) {
            if (StripeUtil.PAYMENT_UNPAID.equals(subLog.getPaymentStatus())) {
                // 未支付，先检查stripe是否支付
                session = StripeUtil.getCheckoutSession(active, subLog.getCheckoutSessionId());
                if (StripeUtil.PAYMENT_UNPAID.equals(session.getPaymentStatus())) {
                    return subLog.getUrl();
                }
                if (subLog.getUnsubEventId() == null) {
                    // 刚刚支付，未取消订阅，webhook还没触发，则更新订阅
                    appUser = aiAppUsersService.updateSub(user.getSocialUserId(), user.getSocialType(), user.getStripeCustomerId(), reqVO.getAppUuid(), reqVO.getMonthNum());

                    medsciUsersService.updateExpireAt(subLog.getSocialUserId(), subLog.getSocialType(), appUser.getExpireAt());
                    throw exception(ErrorCodeConstants.ERROR_5030);
                }
            } else {
                if (subLog.getUnsubEventId() == null) {
                    // 已支付，未取消订阅，提示已支付，可能webhook 未触发
                    appUser = aiAppUsersService.updateSub(user.getSocialUserId(), user.getSocialType(), user.getStripeCustomerId(), reqVO.getAppUuid(), reqVO.getMonthNum());

                    medsciUsersService.updateExpireAt(subLog.getSocialUserId(), subLog.getSocialType(), appUser.getExpireAt());
                    throw exception(ErrorCodeConstants.ERROR_5030);
                }
            }
        }

        // 没有有效订阅链接，创建订阅链接
        session = StripeUtil.createCheckoutSession(active, user.getStripeCustomerId(), reqVO.getPriceId(), successUrl, cancelUrl);
        if (session == null) {
            throw exception(ErrorCodeConstants.ERROR_5031);
        }

        subLog = new AiSubscriptionLog();
        subLog.setSocialUserId(user.getSocialUserId());
        subLog.setSocialType(user.getSocialType());
        subLog.setAppUuid(reqVO.getAppUuid());
        subLog.setStripeCustomerId(user.getStripeCustomerId());
        subLog.setPriceId(reqVO.getPriceId());
        subLog.setPayAmount(payAmount);
        subLog.setCheckoutSessionId(session.getId()); // 存stripe sessionId
        subLog.setPaymentStatus(session.getPaymentStatus());
        subLog.setUrl(session.getUrl());
        subLog.setMonthNum(reqVO.getMonthNum());
        subLog.setExpiredAt(CommonUtil.getLocalDateTime(session.getExpiresAt()));
        subLog.setCreatedAt(CommonUtil.getLocalDateTime(session.getCreated()));

        if (aiSubscriptionLogService.createSubLog(subLog) < 0) {
            throw exception(ErrorCodeConstants.ERROR_5031);
        }

        return subLog.getUrl();
    }

    /**
     * 取消订阅
     *
     * @param auth
     * @param appUuid
     * @param packageKey
     * @return
     */
    @Override
    public String cancelSubscription(String auth, String appUuid, String packageKey) {
        OAuth2AccessTokenCheckRespDTO authUser = oauthService.checkAccessToken(auth);
        // 1. 获取用户和应用信息
        MedsciUsers user = medsciUsersService.getUserByAuthUser(authUser);
        AiAppLangs appLangs = getByUuid(appUuid);
        
        // 2. 根据应用语言类型处理不同的取消逻辑
        if (appLangs == null || ZH_CN.equals(appLangs.getAppLang())) {
            return cancelPackageSubscription(user, packageKey);
        } else {
            return cancelAppSubscription(user, appUuid);
        }
    }
    
    /**
     * 取消中文应用的订阅
     * 
     * @param user 用户信息
     * @return 取消的订阅ID，如果是免费订阅则返回null
     */
    private String cancelPackageSubscription(MedsciUsers user, String packageKey) {
        // 1. 获取用户当前订阅套餐
        AiAppUserPackage userPackage = aiAppUserPackageService.getUserPackageOnSub(user.getSocialUserId(), user.getSocialType(), packageKey);
        
        // 2. 检查是否为免费订阅，免费订阅不处理
        if (userPackage!=null && FeeTypeVo.FREE_TYPE.equals(userPackage.getPackageType())) {
            return null;
        }
        
        // 3. 检查是否有退订中的订阅，有则不处理
        AiAppUserPackage otherPackage = aiAppUserPackageService.getUserPackageOnCancel(user.getSocialUserId(), user.getSocialType(), packageKey);
        if (userPackage==null || otherPackage != null) {
            return null;
        }
        
        // 4. 获取订阅日志并取消订阅
        AiSubscriptionLog subLog = aiSubscriptionLogService.getByCheckoutSessionId(userPackage.getCheckoutSessionId());
        if (subLog == null || StringUtils.isBlank(subLog.getSubId())) {
            throw exception(ErrorCodeConstants.ERROR_5032);
        }
        
        // 5. 调用支付宝取消订阅接口
        if (BaseConstant.PROD_STR.equals(active)) {
            MedsciSubUtil.cancelSub(subLog.getSubId(), true);
        } else {
            mockMedsciSubService.cancelSub(subLog.getSubId(), true);
        }
        return subLog.getSubId();
    }
    
    /**
     * 取消外语应用的订阅
     * 
     * @param user 用户信息
     * @param appUuid 应用UUID
     * @return 取消的订阅ID，如果是免费订阅则返回null
     */
    private String cancelAppSubscription(MedsciUsers user, String appUuid) {
        // 1. 验证应用ID
        if (StringUtils.isBlank(appUuid)) {
            throw exception(ErrorCodeConstants.ERROR_5009);
        }
        
        // 2. 获取订阅日志
        AiSubscriptionLog subLog = aiSubscriptionLogService.getByAppUser(user.getSocialUserId(), user.getSocialType(), appUuid);
        if (subLog == null) {
            throw exception(ErrorCodeConstants.ERROR_5032);
        }
        
        // 3. 检查是否为免费订阅，免费订阅不处理
        if (StringUtils.isBlank(subLog.getSubId())) {
            return null;
        }

        // 4. 调用Stripe取消订阅接口
        StripeUtil.cancelSubscription(active, subLog.getSubId());
        return subLog.getSubId();
    }

    /**
     * stripe事件处理，alipay订阅事件处理
     * @param sigHeader
     * @param payload
     */
    @Override
    public void webhook(String sigHeader, String payload) {
        if (MedsciSubUtil.FreeEvent.equals(sigHeader)) {
            // 处理订阅免费事件
            handleFreeEvent(sigHeader, payload);
        } else if (MedsciSubUtil.AlipaySubEvent.equals(sigHeader)) {
            // alipay订阅事件处理
            aliWebHook(sigHeader, payload);
        } else {
            // stripe事件处理
            stripeWebHook(sigHeader, payload);
        }
    }

    /**
     * Stripe国际支付 订阅事件处理
     * @param sigHeader
     * @param payload
     */
    private void stripeWebHook(String sigHeader, String payload) {
        Event event = StripeUtil.webhook(active, sigHeader, payload);
        if (event == null) {
            return;
        }

        // Deserialize the nested object inside the event
        EventDataObjectDeserializer dataObjectDeserializer = event.getDataObjectDeserializer();
        StripeObject stripeObject = null;
        if (dataObjectDeserializer.getObject().isPresent()) {
            stripeObject = dataObjectDeserializer.getObject().get();
        } else {
            // Deserialization failed, probably due to an API version mismatch.
            // Refer to the Javadoc documentation on `EventDataObjectDeserializer` for
            // instructions on how to handle this case, or return an error here.
            log.error("Deserialization failed, probably due to an API version mismatch");
            return;
        }

        log.error("event type: {}", event.getType());

        switch (event.getType()) {
            case "customer.subscription.created":
            case "customer.subscription.updated":
            case "customer.subscription.paused":
                aiStripeEventLogService.insert(event.getId(), event.getType(), stripeObject.toJson());
                break;

            case "customer.subscription.deleted":
                aiStripeEventLogService.insert(event.getId(), event.getType(), stripeObject.toJson());
                Subscription subscription = (Subscription) stripeObject;
                handleDelete(event.getId(), subscription);
                break;

            case "checkout.session.completed":
                aiStripeEventLogService.insert(event.getId(), event.getType(), stripeObject.toJson());
                Session session = (Session) stripeObject;
                handleSub(event.getId(), session);
                break;

            case "invoice.paid":
                aiStripeEventLogService.insert(event.getId(), event.getType(), stripeObject.toJson());
                Invoice invoice = (Invoice) stripeObject;
                handlePaid(event.getId(), invoice);
                break;

            case "payment_intent.succeeded":
                aiStripeEventLogService.insert(event.getId(), event.getType(), stripeObject.toJson());
                PaymentIntent paymentIntent = (PaymentIntent) stripeObject;
                handlePaid(event.getId(), paymentIntent);
                break;

            default:
                log.error("Unhandled event type: {}", event.getType());
        }
    }

    /**
     * 删除订阅处理
     * @param eventId
     * @param subscription
     */
    @Transactional(rollbackFor = Exception.class)
    private void handleDelete(String eventId, Subscription subscription) {
        AiSubscriptionLog subLog = aiSubscriptionLogService.getBySubId(subscription.getId());
        if (subLog != null) {
            subLog.setUnsubEventId(eventId);
            subLog.setUpdatedAt(LocalDateTime.now());
            aiSubscriptionLogService.updateSubLog(subLog);
            aiStripeEventLogService.insert(eventId, "handleDelete.sub.deleted", JsonUtils.toJsonString(subLog));
        } else {
            aiStripeEventLogService.insert(eventId, "handleDelete.subLog.is.null", BaseConstant.EMPTY_STR);
            log.error("handleDelete.subLog is null");
        }
    }

    /**
     * 处理订阅
     * @param eventId
     * @param session
     */
    @Transactional(rollbackFor = Exception.class)
    private void handleSub(String eventId, Session session) {
        AiSubscriptionLog subLog = aiSubscriptionLogService.getByCheckoutSessionId(session.getId());
        if (subLog != null) {
            MedsciUsers user = medsciUsersService.getBySocialUserId(subLog.getSocialType(), subLog.getSocialUserId());
            // 更新订阅id
            aiSubscriptionLogService.updateSubId(subLog.getId(), session.getSubscription());

            // 创建用户-app关系
            aiAppUsersService.getOrCreateAppUser(user.getSocialUserId(), user.getSocialType(), subLog.getStripeCustomerId(), subLog.getAppUuid());

            updateUseNum(subLog.getAppUuid());
        } else {
            aiStripeEventLogService.insert(eventId, "handleSub.subLog.is.null", session.toJson());
            log.error("handlePaid: subLog is null");
        }
    }

    /**
     * 成功支付处理
     * @param eventId
     * @param paymentIntent
     */
    @Transactional(rollbackFor = Exception.class)
    private void handlePaid(String eventId, PaymentIntent paymentIntent) {
        Invoice invoice = StripeUtil.getInvoice(active, paymentIntent.getInvoice());
        handlePaid(eventId, invoice);
    }

    /**
     * 成功支付处理
     * @param eventId
     * @param invoice
     */
    @Transactional(rollbackFor = Exception.class)
    private void handlePaid(String eventId, Invoice invoice) {
        AiSubscriptionLog subLog = aiSubscriptionLogService.getBySubId(invoice.getSubscription());

        if (subLog != null) {
            MedsciUsers user = medsciUsersService.getBySocialUserId(subLog.getSocialType(), subLog.getSocialUserId());
            subLog.setPaymentStatus(StripeUtil.PAYMENT_PAID);
            subLog.setPiId(invoice.getId());
            subLog.setStripeCustomerId(invoice.getCustomer());
            subLog.setPaymentNum(subLog.getPaymentNum()+BaseConstant.ONE); // 支付次数加1
            subLog.setUpdatedAt(LocalDateTime.now());
            aiSubscriptionLogService.updateSubLog(subLog);

            // 支付成功，每次只将过期时间延后MonthNum月
            AiAppUsers appUser = aiAppUsersService.updateSub(user.getSocialUserId(), user.getSocialType(), subLog.getStripeCustomerId(), subLog.getAppUuid(), subLog.getMonthNum());

            medsciUsersService.updateExpireAt(subLog.getSocialUserId(), subLog.getSocialType(), appUser.getExpireAt());
        } else {
            aiStripeEventLogService.insert(eventId, "handlePaid.subLog.is.null", invoice.toJson());
            log.error("handlePaid.subLog is null");
        }
    }

    @Override
    public String getLanguages(String lang){
        JSONObject langDict = yudaoSystemService.getLangDict();
        return langDict.getString(lang) == null ? BaseConstant.EMPTY_STR : langDict.getString(lang);
    }
    /**
     * 主站用户 支付宝订阅
     * @param user
     * @param reqVO
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    private String createAliSub(MedsciUsers user, CreateSubReqVO reqVO, FeeTypeVo feeType, Integer packageId) {
        redisManage.deleteAllUserPackage(user.getSocialType(), user.getSocialUserId()); // 订阅时清掉免费订阅标识

        Boolean needPay = !feeType.getType().equals(FeeTypeVo.FREE_TYPE);

        // 在检查是否有有效订阅链接
        AiSubscriptionLog subLog = aiSubscriptionLogService.getLastValid(user.getSocialUserId(), user.getSocialType(), reqVO.getAppUuid(), reqVO.getPriceId());
        Map<String, Object> urlMap = new HashMap<>();
        if (subLog != null) {
            log.error("当前有效订阅链接:{}", subLog);
//            if (subLog.getUnsubEventId() == null) {
//                // 未取消订阅
//                throw exception(ErrorCodeConstants.ERROR_5028);
//            }
            if (needPay){
                if (subLog.getMonthNum().equals(reqVO.getMonthNum())) {
                    if (StripeUtil.PAYMENT_UNPAID.equals(subLog.getPaymentStatus())) {
                        urlMap.put("piId", subLog.getPiId());
                        urlMap.put("sessionId", subLog.getCheckoutSessionId());
                        urlMap.put("socialUserId", user.getSocialUserId());
                        urlMap.put("openid", user.getOpenid());
                        return JsonUtils.toJsonString(urlMap);
                    }
                } else {
                    // 当前点击另一个包，则更新 有效订阅链接 为过期
                    subLog.setExpiredAt(subLog.getCreatedAt());
                    aiSubscriptionLogService.updateSubLog(subLog);
                    log.info("当前点击另一个包:{}", reqVO);
                }
            } else {
                // 当前点击免费的，则更新 有效订阅链接 为过期
                subLog.setExpiredAt(subLog.getCreatedAt());
                aiSubscriptionLogService.updateSubLog(subLog);
                log.error("当前点击免费的:{}", reqVO);
            }
        }

        LocalDateTime now = LocalDateTime.now();
        // 创建订阅,首期sessionId 和 orderId 一一对应，续期sessionId 和 orderId 一对多
        String nanoId = IdUtil.nanoId();
        String sessionId = MedsciSubUtil.Alipay + "_session_" + nanoId;
        String orderId = MedsciSubUtil.Alipay + "_order_" + nanoId;
        HttpServletRequest request = ServletUtils.getRequest();
        String url = request.getHeader("referer");
        log.info("订阅url-referer: {}", url);

        subLog = new AiSubscriptionLog();
        subLog.setSocialUserId(user.getSocialUserId());
        subLog.setSocialType(user.getSocialType());
        subLog.setAppUuid(reqVO.getAppUuid());
        subLog.setStripeCustomerId(user.getStripeCustomerId());
        subLog.setPriceId(feeType.getPriceId());
        subLog.setPayAmount(feeType.getFeePrice());
        subLog.setPaymentStatus(StripeUtil.PAYMENT_UNPAID);
        subLog.setMonthNum(feeType.getMonthNum());
        subLog.setCreatedAt(now);
        subLog.setExpiredAt(now.plusDays(BaseConstant.ONE));
        subLog.setUrl(url);
        subLog.setCheckoutSessionId(sessionId);
        subLog.setPiId(orderId);
        subLog.setPackageId(packageId);
        if (aiSubscriptionLogService.createSubLog(subLog) <= BaseConstant.ZERO) {
            throw exception(ErrorCodeConstants.ERROR_5031);
        }

        // 创建订单，计算订单开始时间和截止时间，package appuser的开始时间和截止时间也以此为准
        AiSubOrders aiSubOrders = AiSubOrders.builder()
                .socialUserId(user.getSocialUserId())
                .socialType(user.getSocialType())
                .checkoutSessionId(sessionId)
                .piId(orderId)
                .paymentStatus(StripeUtil.PAYMENT_UNPAID)
                .payAmount(feeType.getFeePrice())
                .needPay(needPay ? BaseConstant.ONE : BaseConstant.ZERO)
                .startAt(needPay ? now : now.withDayOfMonth(BaseConstant.ONE).with(LocalTime.MIN)) // 免费的应用从1号开始计算
                .createdAt(now).build();
        if (FeeTypeVo.PERIOD_DAY.equals(feeType.getPeriodType())) { // 判断按天还是按月
            aiSubOrders.setExpireAt(aiSubOrders.getStartAt().plusDays(feeType.getMonthNum()));
        } else {
            aiSubOrders.setExpireAt(aiSubOrders.getStartAt().plusMonths(feeType.getMonthNum()));
        }
        if (aiSubOrdersService.createOrder(aiSubOrders) <= BaseConstant.ZERO) {
            throw exception(ErrorCodeConstants.ERROR_5034);
        }

        if (needPay) {
            urlMap.put("piId", orderId);
            urlMap.put("sessionId", sessionId);
            urlMap.put("socialUserId", user.getSocialUserId());
            urlMap.put("openid", user.getOpenid());
            return JsonUtils.toJsonString(urlMap);
        } else {
            return url;
        }
    }

    /**
     * 创建所有中文应用用户，同步AiAppUserPackage AiAppUsers 的开始时间及截止时间
     *
     * @return
     */
    @Override
    public AiAppUsers createAppUsersByOrder(AiSubOrders subOrder, Integer packageId, String appUuid) {
        redisManage.deleteAllUserPackage(subOrder.getSocialType(), subOrder.getSocialUserId()); // 订阅成功后清掉免费订阅标识

        AiAppUserPackage nowSub = aiAppUserPackageService.getById(packageId); // 获取当前订阅的包

        // 查询当前用户所有PackageKey订阅的包
        List<AiAppUserPackage> userPackages = aiAppUserPackageService.selectList(UserPackageParam.builder()
                .socialUserId(subOrder.getSocialUserId())
                .socialType(subOrder.getSocialType())
                .packageKey(nowSub.getPackageKey())
                .build());
        LocalDateTime now = LocalDateTime.now();
        for (AiAppUserPackage userPackage : userPackages) {
            if ( userPackage.getPackageType().equals(nowSub.getPackageType())) {
                userPackage.setCheckoutSessionId(subOrder.getCheckoutSessionId());
                userPackage.setSubStatus(BaseConstant.ONE);
                userPackage.setStartAt(subOrder.getStartAt());
                userPackage.setExpireAt(subOrder.getExpireAt());
                userPackage.setSubAt(userPackage.getSubAt()==null?now:userPackage.getSubAt());
                userPackage.setUnSubAt(null);
                userPackage.setUpdatedAt(now);
                aiAppUserPackageService.updateById(userPackage);
            } else {
                userPackage.setSubStatus(BaseConstant.ZERO);
                userPackage.setCheckoutSessionId(BaseConstant.EMPTY_STR);
                userPackage.setUpdatedAt(now);
                aiAppUserPackageService.updateById(userPackage);
            }
        }

        AiAppUsers appUser = null;
        if (AiAppUserPackageService.PACKAGE_ALL_APPS.equals(nowSub.getPackageKey())) {
            // 是中文应用 默认订阅包
            // 内部用户查询全部应用，非内部用户查询对外应用
            String isInternal = medsciUsersService.getIsInternalUser(subOrder.getSocialUserId(), subOrder.getSocialType());
            List<Integer>isInternalUser = BaseConstant.ONE.toString().equals(isInternal) ?
                    Arrays.asList(BaseConstant.ZERO, BaseConstant.ONE) : Arrays.asList(BaseConstant.ZERO);
            List<AiAppLangs> list = selectList(AiAppParam.builder().appLang(ZH_CN).isInternalUsers(isInternalUser).build());
            for (AiAppLangs appLangs : list) {
                appUser = aiAppUsersService.updateSub(subOrder.getSocialUserId(), subOrder.getSocialType(), appLangs.getAppUuid(), subOrder.getStartAt(), subOrder.getExpireAt());
            }
        } else {
            // 是活动的订阅包,注意目前只能是中文应用
            AiAppLangs appLangs = getByNameEn(nowSub.getPackageKey(), BaseConstant.ZH);
            if (!ZH_CN.equals(appLangs.getAppLang())) {
                appLangs = getByUuid(appUuid);
            }
            appUser = aiAppUsersService.updateSub(subOrder.getSocialUserId(), subOrder.getSocialType(), appLangs.getAppUuid(), subOrder.getStartAt(), subOrder.getExpireAt());
        }

        return appUser;
    }

    /**
     * 创建支付宝订阅
     * @param piId
     * @param sessionId
     * @param socialUserId
     * @param openid
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public String createAliSub(String piId, String sessionId, Long socialUserId, String openid) {
        String url = BaseConstant.SLASH_STR;
        AiSubOrders subOrder = aiSubOrdersService.getByPiId(piId);
        AiSubscriptionLog subLog = aiSubscriptionLogService.getByCheckoutSessionId(sessionId);
        if (subOrder == null || subLog == null || !piId.equals(subLog.getPiId())) {
            throw exception(ErrorCodeConstants.ERROR_5035);
        }
        if (!socialUserId.equals(subOrder.getSocialUserId())) {
            throw exception(ErrorCodeConstants.ERROR_5037);
        }
        if (StripeUtil.PAYMENT_PAID.equals(subOrder.getPaymentStatus()) || !BaseConstant.ONE.equals(subOrder.getNeedPay())) {
//            throw exception(ErrorCodeConstants.ERROR_5030);
            return url;
        }
        if (StringUtils.isNotBlank(subLog.getUrl()) && subLog.getUrl().startsWith("alipays://") ) {
            return subLog.getUrl();
        }

        // 获取订阅的周期类型
        AiAppUserPackage aiAppUserPackage = aiAppUserPackageService.getById(subLog.getPackageId());
        String periodType = aiAppUserPackageService.getPeriodType(aiAppUserPackage.getPackageKey(), aiAppUserPackage.getPackageType());

        ContractDataReqVO param = new ContractDataReqVO();
        param.setSignScene(subLog.getPriceId());
        param.setUserId(subOrder.getSocialType()+BaseConstant.COLON_STR+subOrder.getSocialUserId());
        param.setOrderId(subOrder.getPiId());
        param.setPeriodType(periodType);
        param.setPeriod(Long.valueOf(subLog.getMonthNum()));
        param.setSingleAmount(subOrder.getPayAmount().toString());
        if (BaseConstant.PROD_STR.equals(active)) {
            url =  MedsciSubUtil.createSub(param, true);
        } else {
            url =  mockMedsciSubService.createSub(param, true);
        }
        if (BaseConstant.SLASH_STR.equals(url)) {
            throw exception(ErrorCodeConstants.ERROR_5031);
        }

        subLog.setUrl(url);
        aiSubscriptionLogService.updateSubLog(subLog);
        return url;
    }

    /**
     * 支付宝事件 处理
     * @param sigHeader
     * @param payload
     */
    private void aliWebHook(String sigHeader, String payload) {
        String eventId = MedsciSubUtil.Alipay + BaseConstant.UNDER_LINE_STR + IdUtil.nanoId();
        aiStripeEventLogService.insert(eventId, sigHeader, payload);

        PayNotifyDto payNotifyDto = null;
        try {
            payNotifyDto = JsonUtils.parseObject(payload, PayNotifyDto.class);
        } catch (Exception e) {
            log.error("支付宝事件转payNotifyDto报错：{}", e.getMessage());
        }
        if (payNotifyDto != null && StringUtils.isNotBlank(payNotifyDto.getAppOrderId()) && PayStatus.PAID.equals(payNotifyDto.getPayStatus())) {
            // 支付成功
            handlePaid(eventId, payNotifyDto);
            return;
        }

        CreateMedsciMemberSignDto request = null;
        try {
            request = JsonUtils.parseObject(payload, CreateMedsciMemberSignDto.class);
        } catch (Exception e) {
            log.error("支付宝事件转CreateMedsciMemberSignDto报错：{}", e.getMessage());
        }

        if (request != null && StringUtils.isNotBlank(request.getSourceOrderId())) {
            // 订阅并构建支付
            handleSub(eventId, request);
            return;
        }
        if (request != null && StringUtils.isNotBlank(request.getAgreementNo())){
            // 取消订阅
            handleDelete(eventId, request);
            return;
        }
    }

    /**
     * 处理支付成功事件
     * @param eventId
     * @param payNotifyDto
     */
    @Transactional(rollbackFor = Exception.class)
    private void handlePaid(String eventId, PayNotifyDto payNotifyDto) {
        aiStripeEventLogService.insert(eventId, MedsciSubUtil.PaidEvent, BaseConstant.EMPTY_STR);

        AiSubOrders subOrder = aiSubOrdersService.getByPiId(payNotifyDto.getAppOrderId());
        if (subOrder == null) {
            aiStripeEventLogService.insert(eventId, "handlePaid.subOrder.is.null", BaseConstant.EMPTY_STR);
            log.error("handlePaid.subLog is null");
        }
        AiSubscriptionLog subLog = aiSubscriptionLogService.getByCheckoutSessionId(subOrder.getCheckoutSessionId());
        if (subLog != null) {
            subLog.setPaymentStatus(StripeUtil.PAYMENT_PAID);
            subLog.setPaymentNum(subLog.getPaymentNum()+BaseConstant.ONE); // 支付次数加1
            subLog.setUpdatedAt(LocalDateTime.now());
            aiSubscriptionLogService.updateSubLog(subLog);

            aiSubOrdersService.updatePaid(subOrder.getId());

            // 支付成功，每次只将过期时间延后MonthNum月
            AiAppUsers appUser = createAppUsersByOrder(subOrder, subLog.getPackageId(), subLog.getAppUuid());

            medsciUsersService.updateExpireAt(subLog.getSocialUserId(), subLog.getSocialType(), appUser.getExpireAt());
        } else {
            aiStripeEventLogService.insert(eventId, "handlePaid.subLog.is.null", BaseConstant.EMPTY_STR);
            log.error("handlePaid.subLog is null");
        }
    }

    /**
     * 处理支付宝订阅事件
     * @param eventId
     * @param request
     */
    @Transactional(rollbackFor = Exception.class)
    private void handleSub(String eventId, CreateMedsciMemberSignDto request) {
        aiStripeEventLogService.insert(eventId, MedsciSubUtil.CreateSubEvent, BaseConstant.EMPTY_STR);

        // 根据订单id查询
        AiSubscriptionLog subLog = aiSubscriptionLogService.getByPiId(request.getSourceOrderId());
        if (subLog != null) {
            // 构建支付订单
            String payOrderId = null;
            if (BaseConstant.PROD_STR.equals(active)) {
                payOrderId = MedsciSubUtil.payBuild(request.getSourceOrderId(), request.getAgreementNo(), true);
            } else {
                 payOrderId = mockMedsciSubService.payBuild(request.getSourceOrderId(), request.getAgreementNo(), true);
            }

            if (payOrderId == null) {
                aiStripeEventLogService.insert(eventId, "handleSub.payBuild.is.null",  BaseConstant.EMPTY_STR);
                return;
            }
            MsWechatUtil.sendQyWechat(active+"支付宝订阅支付："+payOrderId);

            // 更新支付宝订阅id
            aiSubscriptionLogService.updateSubId(subLog.getId(), request.getAgreementNo());
        } else {
            aiStripeEventLogService.insert(eventId, "handleSub.subLog.is.null",  BaseConstant.EMPTY_STR);
            log.error("handleSub.subLog is null");
        }
    }

    /**
     * 处理支付宝取消订阅事件
     * @param eventId
     * @param request
     */
    @Transactional(rollbackFor = Exception.class)
    private void handleDelete(String eventId, CreateMedsciMemberSignDto request) {
        AiSubscriptionLog subLog = aiSubscriptionLogService.getBySubId(request.getAgreementNo());
        if (subLog != null) {
            subLog.setUnsubEventId(eventId);
            subLog.setUpdatedAt(LocalDateTime.now());
            aiSubscriptionLogService.updateSubLog(subLog);

            //  取消订阅,更新订阅包为 退订中
            AiAppUserPackage userPackage = AiAppUserPackage.builder()
                    .id(subLog.getPackageId())
                    .subStatus(BaseConstant.THREE)
                    .unSubAt(LocalDateTime.now())
                    .updatedAt(LocalDateTime.now()).build();
            aiAppUserPackageService.updateById(userPackage);

            aiStripeEventLogService.insert(eventId, MedsciSubUtil.CancelSubEvent, JsonUtils.toJsonString(subLog));
        } else {
            aiStripeEventLogService.insert(eventId, "handleDelete.subLog.is.null", BaseConstant.EMPTY_STR);
            log.error("handleDelete.subLog is null");
        }
    }

    /**
     * 处理免费事件
     * @param eventType
     * @param orderId
     */
    @Transactional(rollbackFor = Exception.class)
    private void handleFreeEvent(String eventType, String orderId) {
        String eventId = eventType + BaseConstant.UNDER_LINE_STR + IdUtil.nanoId();
        aiStripeEventLogService.insert(eventId, eventType, BaseConstant.EMPTY_STR);
        AiSubOrders subOrder = aiSubOrdersService.getByPiId(orderId);
        if (subOrder != null) {
            // 支付成功
            aiSubOrdersService.updatePaid(subOrder.getId());

            // 更新支付宝订阅id 支付ID
            AiSubscriptionLog subLog = aiSubscriptionLogService.getByCheckoutSessionId(subOrder.getCheckoutSessionId());
            subLog.setPaymentStatus(StripeUtil.PAYMENT_PAID);
            subLog.setPiId(subOrder.getPiId());
            subLog.setPaymentNum(subLog.getPaymentNum()+BaseConstant.ONE); // 支付次数加1
            subLog.setUpdatedAt(LocalDateTime.now());
            if (BaseConstant.ONE.equals(subLog.getPaymentNum())) {
                subLog.setSubAt(LocalDateTime.now());
                subLog.setSubId(subOrder.getCheckoutSessionId());
            }
            aiSubscriptionLogService.updateSubLog(subLog);

            AiAppLangs appLangs = getByUuid(subLog.getAppUuid());
            AiAppUsers appUser = null;
            if (appLangs!=null && !appLangs.getAppLang().equals(ZH_CN)) {
                // 支付成功，每次只将过期时间延后MonthNum月
                appUser = aiAppUsersService.updateSub(subLog.getSocialUserId(), subLog.getSocialType(), subLog.getStripeCustomerId(), subLog.getAppUuid(), subLog.getMonthNum());
            } else {
                // 中文需要完善全部中文应用
                appUser = createAppUsersByOrder(subOrder, subLog.getPackageId(), subLog.getAppUuid());
            }

            medsciUsersService.updateExpireAt(subLog.getSocialUserId(), subLog.getSocialType(), appUser.getExpireAt());
        } else {
            aiStripeEventLogService.insert(eventId, eventType+".is.null", BaseConstant.EMPTY_STR);
        }
    }

    /**
     * 根据订单id查询
     * @param orderId
     * @return
     */
    @Override
    public BusinessQueryOrderResponse getSubLog(String orderId) {
        AiSubOrders order = aiSubOrdersService.getByPiId(orderId);
        if (order == null) {
            throw exception(ErrorCodeConstants.ERROR_5035);
        }

        BusinessQueryOrderResponse res = new BusinessQueryOrderResponse();
        res.setAppOrderId(order.getPiId());
        res.setPayAmount(order.getPayAmount().toString());
        res.setItemOrigAmt(order.getPayAmount());
        res.setItemId(Long.valueOf(order.getId()));
        res.setMobile(BaseConstant.EMPTY_STR);
        res.setCreatedTime(order.getCreatedAt().format(CommonUtil.DateTimeFormat));
        res.setPayStatus(StripeUtil.PAYMENT_PAID.equals(order.getPaymentStatus())? PayStatus.PAID : PayStatus.PAYING);

        return res;
    }

    /**
     * 每天检查本期订阅是否到期
     *
     * @param expired 到期时间
     * @param needPay
     */
    @Override
    public void handleTask(LocalDateTime expired, Integer needPay) {
        String eventId = MedsciSubUtil.Alipay + BaseConstant.UNDER_LINE_STR + IdUtil.nanoId();
        aiStripeEventLogService.insert(eventId, "handleTask.handleTask.start", BaseConstant.EMPTY_STR);

        // 查询即将到期的订阅订单
        List<AiSubOrders> expiredOrders = aiSubOrdersService.getExpiredOrders(expired, needPay);
        
        // 处理每个到期订单
        Map<String, AiAppLangs> appLangMap = new HashMap<>();
        LocalDateTime now = LocalDateTime.now();
        for (AiSubOrders order : expiredOrders) {
            processExpiredOrder(order, appLangMap, now);
        }
    }

    /**
     * 手动发起扣款，请注意如果订单支付失败会清空package表的checkout_session_id
     * @param orderId
     */
    @Override
    public void handleOrderTask(String orderId) {
        AiSubOrders order = aiSubOrdersService.getByPiId(orderId);
        if (order != null) {
            Map<String, AiAppLangs> appLangMap = new HashMap<>();
            LocalDateTime now = LocalDateTime.now();
            processExpiredOrder(order, appLangMap, now);
        }
    }

    
    /**
     * 处理即将到期的订单
     * @param order 到期订单
     * @param appLangMap 应用语言缓存
     * @param now 当前时间
     */
    public void processExpiredOrder(AiSubOrders order, Map<String, AiAppLangs> appLangMap, LocalDateTime now) {
        String eventId = MedsciSubUtil.Alipay + BaseConstant.UNDER_LINE_STR + IdUtil.nanoId();
        
        // 获取订阅日志
        AiSubscriptionLog subLog = aiSubscriptionLogService.getByCheckoutSessionId(order.getCheckoutSessionId());
        if (subLog == null) {
            aiStripeEventLogService.insert(eventId, "handleTask.subLog.is.null", JsonUtils.toJsonString(order));
            return;
        }
        if (StringUtils.isNotBlank(subLog.getUnsubEventId())) {
            // 退订的订单没有下一期
            aiStripeEventLogService.insert(eventId, "handleTask.subLog.is.unSub", JsonUtils.toJsonString(order));
            return;
        }
        
        // 获取应用语言信息
        AiAppLangs appLang = getAppLangFromMap(appLangMap, subLog.getAppUuid());
        
        // 获取下一期订单
        AiSubOrders nextOrder = getNextOrder(order, now, subLog.getMonthNum());
        
        // 根据应用语言类型处理订单
        if (appLang == null || ZH_CN.equals(appLang.getAppLang())) {
            processPackageOrder(subLog, nextOrder, eventId);
        } else {
            processAppOrder(appLang, order, nextOrder, eventId);
        }
    }
    
    /**
     * 从缓存中获取应用语言信息，如果缓存中不存在则从数据库获取并加入缓存
     * @param appLangMap 应用语言缓存
     * @param appUuid 应用UUID
     * @return 应用语言信息
     */
    private AiAppLangs getAppLangFromMap(Map<String, AiAppLangs> appLangMap, String appUuid) {
        String cacheKey = appUuid.replace("-", "");
        AiAppLangs appLang = appLangMap.get(cacheKey);
        if (appLang == null) {
            appLang = getByUuid(appUuid);
            if (appLang != null) {
                appLangMap.put(cacheKey, appLang);
            }
        }
        return appLang;
    }
    
    /**
     * 创建下一期订单
     * @param currentOrder 当前订单
     * @param now 当前时间
     * @return 新创建的订单
     */
    private AiSubOrders getNextOrder(AiSubOrders currentOrder, LocalDateTime now, Integer monthNum) {
        String orderId = MedsciSubUtil.Alipay + "_order_" + IdUtil.nanoId();
        return AiSubOrders.builder()
                .socialUserId(currentOrder.getSocialUserId())
                .socialType(currentOrder.getSocialType())
                .checkoutSessionId(currentOrder.getCheckoutSessionId())
                .piId(orderId)
                .paymentStatus(StripeUtil.PAYMENT_UNPAID)
                .startAt(currentOrder.getExpireAt())
                .expireAt(currentOrder.getExpireAt().plusMonths(monthNum))
                .createdAt(now)
                .build();
    }
    
    /**
     * 处理中文应用订单
     * @param subLog 订阅日志
     * @param nextOrder 下一期订单
     * @param eventId 事件ID
     */
    private void processPackageOrder(AiSubscriptionLog subLog, AiSubOrders nextOrder, String eventId) {
        // 获取用户套餐
        AiAppUserPackage userPackage = aiAppUserPackageService.getByCheckoutSessionId(subLog.getCheckoutSessionId());
        if (userPackage == null) {
            if (BaseConstant.ONE.equals(nextOrder.getNeedPay())) {
                // 没有用户套餐且需要支付的，说明是老的单个订阅，主动去取消订阅
                handleLegacySubscription(subLog, eventId, nextOrder.getPiId());
            }
        } else {
            // 处理订阅套餐
            handlePackageSubscription(userPackage, subLog, nextOrder, eventId);
        }
    }
    
    /**
     * 处理老的单个订阅
     * @param subLog 订阅日志
     * @param eventId 事件ID
     * @param orderId 订单ID
     */
    private void handleLegacySubscription(AiSubscriptionLog subLog, String eventId, String orderId) {
        if (StringUtils.isNotBlank(subLog.getSubId())) {
            MedsciSubUtil.cancelSub(subLog.getSubId(), true);
            aiStripeEventLogService.insert(eventId, "handleTask.auto.cancelSub", orderId);
            MsWechatUtil.sendQyWechat(active+"主动去取消老的订阅，订阅id：" + subLog.getSubId());
        } else {
            log.info("handleTask.不需要处理的老订阅");
        }
    }
    
    /**
     * 处理订阅套餐
     * @param userPackage 用户套餐
     * @param subLog 订阅日志
     * @param nextOrder 下一期订单
     * @param eventId 事件ID
     */
    private void handlePackageSubscription(AiAppUserPackage userPackage, AiSubscriptionLog subLog, 
                                          AiSubOrders nextOrder, String eventId) {
        // 获取费用类型
        FeeTypeVo feeType = aiAppUserPackageService.getFeeType(userPackage.getPackageKey(), userPackage.getPackageType());
        nextOrder.setPayAmount(feeType.getFeePrice());
        boolean isFreeType = feeType.getType().equals(FeeTypeVo.FREE_TYPE);
        nextOrder.setNeedPay(isFreeType ? BaseConstant.ZERO : BaseConstant.ONE);

        aiSubOrdersService.createOrder(nextOrder);
        if (isFreeType) {
            // 免费套餐
            aiStripeEventLogService.insert(eventId, "handleTask.free", JsonUtils.toJsonString(nextOrder));
        } else {
            // 付费套餐，需要支付
            aiStripeEventLogService.insert(eventId, "handleTask.payBuild", JsonUtils.toJsonString(nextOrder));
            String payOrderId = null;
            if (BaseConstant.PROD_STR.equals(active)) {
                payOrderId = MedsciSubUtil.payBuild(nextOrder.getPiId(), subLog.getSubId(), true);
            } else {
                payOrderId = mockMedsciSubService.payBuild(nextOrder.getPiId(), subLog.getSubId(), false);
            }
            if (payOrderId == null) {
                aiStripeEventLogService.insert(eventId, "handleTask.payBuild.is.null", nextOrder.getPiId());
            }
            MsWechatUtil.sendQyWechat(active+"支付宝自动扣款支付，订阅id：" + subLog.getSubId());
        }
    }
    
    /**
     * 处理非中文应用订单
     * @param appLang 应用语言信息
     * @param currentOrder 当前订单
     * @param nextOrder 下一期订单
     * @param eventId 事件ID
     */
    private void processAppOrder(AiAppLangs appLang, AiSubOrders currentOrder, AiSubOrders nextOrder, String eventId) {
        boolean preNeedPay = BaseConstant.ONE.equals(currentOrder.getNeedPay());
        boolean nowNeedPay = isCurrentlyNeedPay(appLang);
        
        if (preNeedPay) {
            if (nowNeedPay) {
                // 以前收费，现在仍收费
                aiSubOrdersService.createOrder(nextOrder);
                aiStripeEventLogService.insert(eventId, "handleTask.is.stripePay", JsonUtils.toJsonString(currentOrder));
            } else {
                // 以前收费，现在免费
                aiSubOrdersService.createOrder(nextOrder);
                aiStripeEventLogService.insert(eventId, "handleTask.free", JsonUtils.toJsonString(currentOrder));
            }
        } else {
            if (nowNeedPay) {
                // 以前免费，现在收费，需要以后自行重新订阅
                aiStripeEventLogService.insert(eventId, "handleTask.appLang.freeToNeedPay", JsonUtils.toJsonString(currentOrder));
            } else {
                // 以前免费，现在免费
                aiSubOrdersService.createOrder(nextOrder);
                aiStripeEventLogService.insert(eventId, "handleTask.free", JsonUtils.toJsonString(currentOrder));
            }
        }
    }
    
    /**
     * 判断应用当前是否需要付费
     * @param appLang 应用语言信息
     * @return 是否需要付费
     */
    private boolean isCurrentlyNeedPay(AiAppLangs appLang) {
        // 月费用是开启的，且金额>0，则此应用是收费的
        if (BaseConstant.ONE.equals(appLang.getFeeType()) && BigDecimal.ZERO.compareTo(appLang.getFeePrice()) < 0) {
            return true;
        }
        // 季费用是开启的，且金额>0，则此应用是收费的
        if (BaseConstant.ONE.equals(appLang.getFeeType2()) && BigDecimal.ZERO.compareTo(appLang.getFeePrice2()) < 0) {
            return true;
        }
        // 年费用是开启的，且金额>0，则此应用是收费的
        if (BaseConstant.ONE.equals(appLang.getFeeType3()) && BigDecimal.ZERO.compareTo(appLang.getFeePrice3()) < 0) {
            return true;
        }
        return false;
    }

    @Override
    public List<SubscriptionCountResponse> countSubscriptions() {
        return baseMapper.countSubscriptions();
    }

    @Override
    public AiAppResponse getByUuid(String uuid, String auth) {
        AiAppLangs appLangs = getByUuid(uuid);
        if (appLangs == null || !ONLINE.equals(appLangs.getAppStatus())) {
            return null;
        }

        AiAppResponse res = toBean(appLangs);
        OAuth2AccessTokenCheckRespDTO authUser = oauthService.checkAccessToken(auth);
        if (authUser != null) {
            AiAppUsers appUser = aiAppUsersService.getAppUser(authUser.getUserId(), authUser.getUserType(), appLangs.getAppUuid());
            if (appUser != null && appUser.getExpireAt() != null) {
                res.setAppUser(BeanUtils.toBean(appUser, AiAppUserResponse.class,
                        item1 -> {
                            item1.setExpireAt(appUser.getExpireAt().format(CommonUtil.DateTimeFormat1));
                        }));
            }
        } else {
            log.error("用户不存在{}", authUser);
        }
        return res;
    }

    @Override
    public Integer updatePriceId(UpdatePriceIdReqVO reqVO) {
        AiAppLangs appLangs = baseMapper.selectById(reqVO.getId());
        if (appLangs != null && !appLangs.getAppStatus().equals("删除")) {
            appLangs.setFeePriceId(reqVO.getFee_price_id());
            appLangs.setFeePriceId2(reqVO.getFee_price_id2());
            appLangs.setFeePriceId3(reqVO.getFee_price_id3());
            return baseMapper.updateById(appLangs);
        }

        return BaseConstant.ZERO;
    }

    @Override
    public String getUserAppPackage(Long socialUserId, Integer socialType, String appUuid) {
        String appPackage = redisManage.getUserAppPackage(socialType, socialUserId, appUuid);
        if (appPackage == null) {
            AiAppUsers appUser = aiAppUsersService.getAppUser(socialUserId, socialType, appUuid);
            if (appUser == null || !BaseConstant.ONE.equals(appUser.getStatus())) {
                throw exception(ErrorCodeConstants.ERROR_5047);
            }
            AiAppLangs appLangs = getByUuid(appUuid);
            String packageKey = BaseConstant.PLACE_HOLDER; // 默认使用占位符
            String packageType = BaseConstant.EMPTY_STR;
            if (ZH_CN.equals(appLangs.getAppLang())) {
                packageKey = yudaoSystemService.getConfigByKey(appLangs.getAppNameEn()) == null ?
                        AiAppUserPackageService.PACKAGE_ALL_APPS : appLangs.getAppNameEn();
                AiAppUserPackage appUserPackage = aiAppUserPackageService.getSubOrCancel(socialUserId, socialType, packageKey);
                packageType = appUserPackage == null ? BaseConstant.EMPTY_STR : appUserPackage.getPackageType();
            }
            redisManage.setUserAppPackage(socialType, socialUserId, appUuid, packageKey+BaseConstant.COLON_STR+packageType);
        }
        appPackage = redisManage.getUserAppPackage(socialType, socialUserId, appUuid);
        return appPackage;
    }

    @Override
    public Boolean checkIsUXOFromCache(Long socialUserId, Integer socialType, String appUuid) {
        String nameEn = getAppNameEn(appUuid);
        return yudaoSystemService.checkIsUXOFromCache(socialType, socialUserId, nameEn);
    }

    @Override
    public String getAppNameEn(String appUuid) {
        String nameEn = redisManage.getAppNameEn(appUuid);
        if (StringUtils.isBlank(nameEn)) {
            AiAppLangs appLangs = getByUuid(appUuid);
            if (appLangs != null) {
                nameEn = appLangs.getAppNameEn();
                redisManage.setAppNameEn(appUuid, nameEn);
            } else {
                throw exception(ErrorCodeConstants.ERROR_5027);
            }
        }
        return nameEn;
    }

    @Override
    public Integer getFreeNum(String appUuid) {
        String value = redisManage.getFreeNum(appUuid);
        if (StringUtils.isBlank(value)) {
            String nameEn = getAppNameEn(appUuid);
            ConfigDO configDO = yudaoSystemService.getConfigByKey(AiAppUserPackageService.FREE_NUM);
            value = JSONObject.parseObject(configDO.getValue()).getString(nameEn);
            if (StringUtils.isBlank(value)) {
                value = BaseConstant.ZERO_STR;
            } else {
                redisManage.setFreeNum(appUuid, value);
            }
        }
        return Integer.valueOf(value);
    }

    @Override
    public BindAppUserResponse bindAppUser(String auth, String appUuid, String nameEn) {
        OAuth2AccessTokenCheckRespDTO authUser = oauthService.checkAccessToken(auth);
        BindAppUserResponse res = new BindAppUserResponse();
        res.setSocialUserId(authUser.getUserId());
        res.setSocialType(authUser.getUserType());
        res.setAppUuid(appUuid);

        Integer freeNum = getFreeNum(appUuid);

        if (freeNum > BaseConstant.ZERO) {
            AiAppUsers aiAppUser = aiAppUsersService.getOrCreateAppUser(authUser.getUserId(), authUser.getUserType(), BaseConstant.EMPTY_STR, appUuid);
            aiAppUser.setStatus(BaseConstant.ONE); // 绑定并激活
            aiAppUsersService.updateById(aiAppUser);
        }

        res.setFreeNum(freeNum);
        if (BaseConstant.NEGATIVE_ONE.equals(res.getFreeNum())) {
            // -1 表示不限制
            res.setUseNum(BaseConstant.ONE);
            res.setRemainNum(BaseConstant.ONE);
        } else {
            res.setUseNum(aiAppUsersService.countAll(authUser.getUserId(), authUser.getUserType(), appUuid));
            Integer remainNum = res.getFreeNum() - res.getUseNum();
            res.setRemainNum(remainNum>BaseConstant.ZERO?remainNum:BaseConstant.ZERO);
        }

        return res;
    }

    @Override
    public List<String> getAllDifyUuidFromCache(String active) {
        String value = yudaoSystemService.getConfigByKeyFromCache("dify_uuid", "dify_uuid.white_list");
        if (StringUtils.isNotBlank(value)) {
            JSONObject configData = JSONObject.parseObject(value);
            JSONArray configArr = configData.getJSONArray(active) == null ? new JSONArray() : configData.getJSONArray(active);
            if (configArr.isEmpty()) {
                selectList(AiAppParam.builder().build()).stream().map(AiAppLangs::getDifyAppUuid).collect(Collectors.toSet()).stream().forEach(uuid -> {
                    if (StringUtils.isNotBlank(uuid)) {
                        configArr.add(uuid);
                    }
                });
                configData.put(active, configArr);

                ConfigDO configDO = yudaoSystemService.getConfigByKey("dify_uuid.white_list");
                ConfigSaveReqVO saveReqVO = BeanUtils.toBean(configDO, ConfigSaveReqVO.class);
                saveReqVO.setKey(configDO.getConfigKey());
                saveReqVO.setValue(configData.toJSONString());
                yudaoSystemService.updateConfig(saveReqVO);
                yudaoSystemService.clearConfigCache("dify_uuid:dify_uuid.white_list");
            }
            return configArr.toJavaList(String.class);
        }
        return new ArrayList<>();
    }



}
