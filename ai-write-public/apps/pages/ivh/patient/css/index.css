@font-face {
  font-family: "SourceHanSansCN";
  src: url("./../font/SourceHanSansSC-VF.otf");
}

* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
  font-family: "SourceHanSansCN";

  touch-action: none;
  -webkit-tap-highlight-color: transparent;
}

html,
body {
  overflow: hidden;
  margin: 0px;
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}

button {
  outline: none;
  border: 0;
  background: transparent;
  cursor: pointer;
}

ul,
ol {
  padding: revert;
  padding-inline-start: 25px;
}

.pc {
  background-image: image-set(
    /* url(./../img/medsci-logo.png) 1x, */ /* url(./../img/<EMAIL>) 2x */
  ),
  url(./../img/bg.png);
  background-color: #fff;
  background-size: 161px 27px, cover;
  background-repeat: no-repeat;
  background-position: 50px 50px, 0 0;
}

.wrapper {
  position: absolute;
  left: 50%;
  width: 100%;
  max-width: 62.5vh;
  height: 100vh;
  height: 100dvh;
  border: 0;
  transform: translate(-50%, 0);
  background-image: url(./../img/medsci-bg.png);
  background-color: #fff;
  background-size: 100% 100%;
  background-repeat: no-repeat;
  background-position: 50% 98%, 0 0;
  max-width: 100vw;
  height: 100vh;
  display: flex;
  flex-direction: row;
}

.text-box {
  max-width: 55%;
  min-width: 300px;
  position: absolute;
  width: 60%;
  left: 40%;
  z-index: 999;
}

.text-box > div {
  width: 400px;
  padding: 20px;
  background: #fff;
  border-radius: 8px;
  margin-left: 10px;
  box-sizing: border-box;
}

.wrapper.chat {
  /*background: #fff;*/
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  width: 100vw;
  height: 100vh;
  overflow: hidden;
}

.pc .wrapper {
  width: 390px;
  height: 844px;
  overflow: hidden;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 100vw;
  height: 100vh;
}

.loading {
  width: 100%;
  height: 100%;
}

.loading > div {
  position: absolute;
  bottom: 48.25%;
  width: 100%;
  color: #1892FF;
  text-align: center;
  font-weight: 500;
  font-size: 1.875vh;
  line-height: 1.5;
}

.loading .dot {
  position: relative;
  left: 5px;
  padding-right: 30px;
}

.loading .dot::after {
  position: absolute;
  overflow: hidden;
  width: 1.5em;
  content: ".";
  white-space: nowrap;
  letter-spacing: 0.3em;
  animation: dot 3s infinite step-start;
}

.ai-loading  {
  width: 100%;
  height: 100%;
}

.ai-loading  > div {
  position: absolute;
  bottom: 48.25%;
  width: 100%;
  color: #1892FF;
  text-align: center;
  font-weight: 500;
  font-size: 1.875vh;
  line-height: 1.5;
}

.ai-loading .dot {
  position: relative;
  left: 5px;
  padding-right: 30px;
}

.ai-loading .dot::after {
  position: absolute;
  overflow: hidden;
  width: 1.5em;
  content: ".";
  white-space: nowrap;
  letter-spacing: 0.3em;
  animation: dot 3s infinite step-start;
}

.ai-loading {
  display: none;
}


@keyframes dot {
  0% {
    content: ".";
  }

  33% {
    content: "..";
  }

  66% {
    content: "...";
  }

  100% {
    content: ".";
  }
}

.btn-enter,
.btn-re-enter {
  position: absolute;
  z-index: 9999;
  top: 43%;
  left: 70%;
  display: none;
  width: 120px;
  height: 120px;
  background-image: url(./../img/<EMAIL>);
  background-image: image-set(
      url(./../img/enter.png) 1x,
      url(./../img/<EMAIL>) 2x
  );
  background-position: 50%;
  background-size: cover;
  background-repeat: no-repeat;
  transform: translateX(-50%);
}

.btn-re-enter {
  background-image: url(./../img/<EMAIL>);
  background-image: image-set(
      url(./../img/reenter.png) 1x,
      url(./../img/<EMAIL>) 2x
  );
}

.header {
  display: none;
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 64px;
  color: #fff;
  text-indent: 20px;
  background: transparent;
  z-index: 1;
  padding: 12px 0;
}

.header-bg {
  display: none;
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 64px;
}

.header h1 {
  font-size: 16px;
  font-weight: 500;
  line-height: 24px;
}

.header p {
  font-size: 12px;
  line-height: 16px;
  font-weight: 400;
  color: rgba(255, 255, 255, 0.60);
}

.header.hide {
  background: rgba(0, 0, 0, 0);
  backdrop-filter: initial;
}

.header.hide h1,
.header.hide p {
  display: none;
}

.btn-exit {
  position: absolute;
  top: 16px;
  right: 12px;
  width: 40px;
  height: 40px;
  background-image: image-set(
      url(./../img/exit.png) 1x,
      url(./../img/<EMAIL>) 2x
  );
  background-size: cover;
  background-repeat: no-repeat;

  border-radius: 32px;
  background-blend-mode: luminosity;
  backdrop-filter: blur(50px);
}

.chat {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: flex-start;
  flex: 0 0 40%;
  position: absolute;
  right: 5%;
  top: 50%;
  transform: translateY(-50%);
  height: 80%;
  overflow-y: auto;
  padding: 20px;
  box-sizing: border-box;
  background-color: #fff;
  border-left: 1px solid #ddd;
}

.chat-user {
  display: none;
  align-self: flex-end;
  max-width: 310px;
  height: auto;
  background: #1892FF;
  border-radius: 8px 0 8px 8px;
  color: #fff;
  padding: 10px;
  margin: 5px 0;
  text-align: left;
  box-sizing: border-box;
  overflow: hidden;
}

.chat-user-img {
  background: none !important;
  border-radius: unset !important;
  margin-left: 10px;
  text-align: unset;
  padding: 5px 0 !important;
  align-self: center;
  width: 35px;
}

.chat-ai {
  display: none;
  align-self: flex-start;
  background-color: rgba(247, 248, 250, 0.75);
  color: #000;
  padding: 10px;
  border-radius: 10px;
  margin: 5px 0;
  max-width: 80%;
  min-width: 300px;
}

.chat-ai.stop {
  padding-bottom: 40px;
}

.chat-ai.stop::after {
  content: "已停止生成";
  /* position: absolute; */
  /* bottom: 14px; */
  left: 14px;
  color: rgba(1, 11, 50, 0.41);
  font-size: 15px;
}

.chat-ai > div {
  position: relative;
  overflow-y: auto;
  /*padding: 0 1.4vh 0 0;*/
  max-height: none;
  background: transparent;
  color: rgba(31, 31, 31, 1);
  font-weight: 400;
  font-size: 15px;
  line-height: 22px;

  touch-action: pan-y;
}

.chat-ai > div * {
  touch-action: pan-y;
}

.chat-ai > div::-webkit-scrollbar {
  width: 0.9375vh;
  background-color: transparent;
}

.chat-ai > div::-webkit-scrollbar-track {
  background: transparent;
}

.chat-ai > div::-webkit-scrollbar-thumb {
  width: 0.9375vh;
  height: 0.9375vh;
  border-radius: 0.52vh;
  background: rgba(0, 0, 0, 0.1);
  outline: none;
}

.chat-ai .title {
  padding-bottom: 1.25vh;
  line-height: 1.6;
}

.chat-ai .title img {
  width: 100%;
}

.chat-ai.chat-ai-text {
  display: none;
  align-self: flex-start;
  width: 80vh;
  min-width: 60%;
  max-height: 60vh;
  border-radius: 8px;
  color: #333333;
  padding: 10px;
  margin: 5px 0 5px 20px;
  text-align: left;
  box-sizing: border-box;
  overflow: auto;
  background-color: #fff;
}

.chat-ai-text pre {
  overflow: hidden;
  white-space: pre-wrap;
  word-break: break-word;
  font-family: PingFangSC-Regular;
  font-weight: 400;
  font-size: 16px;
  color: #333333;
  line-height: 1.6;
}

.chat-ai-text pre pre {
  padding: 1em;
  background: #282c34;
  color: #abb2bf;
}

.chat-ai-text pre img {
  width: 100%;
}

.chat-ai-text pre table {
  width: 100%;
}

.chat-ai-text pre th,
.chat-ai-text pre td,
.chat-ai-text pre table {
  border: 1px solid;
  border-collapse: collapse;
  font-size: 12px;
}

.chat-ai-option .list {
  display: grid;
  grid-template-columns: 1fr;
  grid-gap: 1.25vh;
}

.chat-ai-option .list.style2 {
  grid-template-columns: 1fr 1fr;
}

.chat-ai-option button {
  padding: 0.83vh 1.25vh;
  background: linear-gradient(
      100.63deg,
      rgba(95, 156, 255, 0.9) 9.85%,
      rgba(38, 111, 232, 0.9) 106.01%
  );
  border-radius: 0.83vh;
  font-weight: 500;
  font-size: 1.5625vh;
  line-height: 1.5;
  color: #fff;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.chat-ai-option .style2 button {
  width: 100%;
}

.chat-ai-image {
  width: 42.03vh;
}

.chat-ai-image .image {
  cursor: pointer;
  background-repeat: no-repeat;
  background-size: contain;
  background-position: 50%;
  background-color: rgba(105, 135, 175, 0.1);
  width: 100%;
  height: 22vh;
  border-radius: 0.83vh;
}

.chat-ai-video {
  width: 42.03vh;
}

.chat-ai-video .video {
  background-color: rgba(105, 135, 175, 0.1);
  border-radius: 0.83vh;
  cursor: pointer;
  width: 100%;
  height: 22vh;
  position: relative;
}

.chat-ai-video .video::before {
  content: "";
  position: absolute;
  width: 100%;
  height: 100%;
  background-repeat: no-repeat;
  background-size: 20%;
  background-position: 50% 45%;
  background-image: url(./../img/play.svg);
}

.chat-ai-video video {
  width: 100%;
  height: 100%;
}

.chat-ai-popup .title {
  font-weight: 400;
  color: #000;
}

.chat-ai-popup .button {
  padding: 0.83vh 1.25vh;
  background: linear-gradient(
      100.63deg,
      rgba(95, 156, 255, 0.9) 9.85%,
      rgba(38, 111, 232, 0.9) 106.01%
  );
  border-radius: 0.83vh;
  font-weight: 500;
  font-size: 1.5625vh;
  line-height: 1.5;
  color: #fff;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.chat-popup {
  z-index: 301;
  position: absolute;
  top: 28.125vh;
  left: 50%;
  transform: translateX(-50%);
  width: 90%;
  height: 50%;
  background: rgba(247, 248, 250, 0.8);
  box-shadow: 0px 0px 0.9375vh rgba(18, 19, 25, 0.06),
  0px 0.3125vh 1.875vh rgba(18, 19, 25, 0.12);
  backdrop-filter: blur(0.46875vh);
  border-radius: 1.67vh;
  padding: 2.7vh 0.475vh 2.7vh 1.875vh;
  display: none;
}

.chat-popup .close {
  position: absolute;
  right: -2vh;
  top: -2vh;
  width: 4.17vh;
  height: 4.17vh;
  background-image: url(./../img/close.svg),
  linear-gradient(115.06deg, #2580ff 23.94%, #5aacf7 95.07%);
  box-shadow: 0vh 0.46875vh 1.25vh rgba(90, 172, 248, 0.3);
  border-radius: 50%;
  background-repeat: no-repeat;
  background-position: 50% 50%, 50% 50%;
  background-size: 37.5%, 100%;
}

.chat-popup > div {
  width: 100%;
  height: 100%;
  position: relative;
  overflow: overlay;
  padding: 0 1.4vh 0 0;
}

.chat-popup > div::-webkit-scrollbar {
  width: 0.9375vh;
  background-color: transparent;
}

.chat-popup > div::-webkit-scrollbar-track {
  background: transparent;
}

.chat-popup > div::-webkit-scrollbar-thumb {
  width: 0.9375vh;
  height: 0.9375vh;
  border-radius: 0.52vh;
  background: rgba(0, 0, 0, 0.1);
  outline: none;
}

.chat-popup img {
  width: calc(100% - 1.875vh);
}

.chat-popup video {
  width: 100%;
  height: 100%;
  background-color: #000;
  border-radius: 0.83vh;
}

.chat-popup iframe {
  width: 100%;
  height: 100%;
}

.chat-marquee {
  position: absolute;
  top: 50%;
  left: 0;
  display: none;
  overflow: hidden;
  margin: -62px 20px 0;
  width: calc(100% - 40px);
  height: 200px;
  /* backdrop-filter: blur(0.3125vh); */
  border-radius: 1.67vh;
  background: rgba(247, 248, 250, 0.8);
  box-shadow: 0vh 0vh 0.9375vh rgba(18, 19, 25, 0.06),
  0vh 0.3125vh 1.875vh rgba(18, 19, 25, 0.12);
}

.chat-marquee .title {
  overflow: hidden;
  padding: 9px;
  width: 100%;
  height: 46px;
  background: rgba(247, 248, 250, 0.8);
  color: #0f1829;
  text-align: center;
  text-overflow: ellipsis;
  white-space: nowrap;
  font-size: 14px;
  line-height: 22px;
}

.chat-marquee .list {
  padding: 5px 0;
}

.chat-marquee .list button {
  display: inline-block;
  overflow: hidden;
  margin: 0px 5px;
  padding: 8px;
  max-width: 200px;
  border-radius: 0.83vh;
  background: linear-gradient(
      100.63deg,
      rgba(95, 156, 255, 0.9) 9.85%,
      rgba(38, 111, 232, 0.9) 106.01%
  );
  color: #fff;
  text-overflow: ellipsis;
  white-space: nowrap;
  font-weight: 500;
  font-size: 14px;
  line-height: 1.5;
}

.chat-marquee .list .marquee {
  display: inline-block;
  padding: 5px;
  height: 46px;
  white-space: nowrap;
  /* transition: all 20s 0s linear; */
}

.chat-marquee .list .marquee p {
  display: inline-block;
}

.asr-pop {
  position: absolute;
  bottom: 0;
  z-index: 100;
  display: none;
  width: 100%;
  background: linear-gradient(
      180deg,
      rgba(0, 0, 0, 0) 12.45%,
      rgba(0, 0, 0, 0.22) 35.64%,
      rgba(0, 0, 0, 0.65) 100%
  );
}

.asr-text {
  position: absolute;
  bottom: 140px;
  display: -webkit-box;
  overflow: hidden;
  -webkit-box-orient: vertical;
  margin: 0 40px;
  max-height: 60px;
  width: calc(100% - 80px);
  color: #228cec;
  text-align: center;
  text-overflow: ellipsis;
  font-weight: 400;
  font-style: normal;
  font-size: 13px;
  line-height: 20px;

  -webkit-line-clamp: 3;
}

.asr-wave {
  position: absolute;
  bottom: 49px;
  left: 50%;
  width: 347px;
  height: 76px;
  background-image: url(./../img/yuyin2x.png);
  background-position: 50% 12%;
  background-size: 76px;
  background-repeat: no-repeat;
  transform: translateX(-50%);
}

.asr-wave canvas {
  position: absolute;
  top: 50%;
  height: 70%;
  transform: translateY(-50%);
}

.asr-wave .wave-left {
  left: 20px;
}

.asr-wave .wave-right {
  right: 20px;
}

.mask {
  position: absolute;
  top: 0px;
  left: 0px;
  z-index: 300;
  display: none;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.15);

  backdrop-filter: blur(0.23vh);
}

.warn {
  position: absolute;
  top: 50%;
  left: 50%;
  z-index: 999999;
  display: none;
  width: 270px;
  border-radius: 1.67vh;
  background: rgba(247, 248, 250, 0.8);
  box-shadow: 0vh 0vh 0.9375vh rgba(18, 19, 25, 0.06),
  0vh 0.3125vh 1.875vh rgba(18, 19, 25, 0.12);
  transform: translate(-50%, -50%);
  padding: 20px 12px 12px;
  backdrop-filter: blur(0.47vh);
  text-align: center;
}

.warn .info {
  display: -webkit-box;
  overflow: hidden;
  -webkit-box-orient: vertical;
  color: #1f1f1f;
  text-align: center;
  text-overflow: ellipsis;
  word-wrap: break-word;
  font-weight: 400;
  font-size: 14px;
  line-height: 22px;
  margin: 8px 0 16px;
  -webkit-line-clamp: 4;
}

.warn button {
  width: 100%;
  height: 36px;
  border-radius: 0.83vh;
  background: linear-gradient(
      100.63deg,
      rgba(95, 156, 255, 0.9) 9.85%,
      rgba(38, 111, 232, 0.9) 106.01%
  );
  color: #fff;
  font-weight: 700;
  font-size: 14px;
  line-height: 22px;
}

.action-wrapper {
  position: absolute;
  bottom: 0;
  width: 100%;
  text-align: center;
}

.audio-action-wrapper {
  display: none;
  position: relative;
  min-height: 106px;
  z-index: 999;
}

.audio-action-wrapper > div {
  display: flex;
  align-items: center;
  justify-content: center;
}

.btn-stop-create,
.btn-recreate {
  display: none;
  width: 108px;
  height: 36px;
  background-size: cover;
  position: absolute;
  bottom: 122px;
  left: 50%;
  transform: translateX(-50%);
  z-index: 999;
}

.audio .btn-stop-create,
.audio .btn-recreate {
  bottom: 142px;
}

.btn-stop-create {
  background-image: image-set(
      url(./../img/stop-create.png) 1x,
      url(./../img/<EMAIL>) 2x
  );
}

.btn-recreate {
  background-image: image-set(
      url(./../img/recreate.png) 1x,
      url(./../img/<EMAIL>) 2x
  );
}

.btn-audio {
  width: 50px;
  height: 50px;
  background: url(./../img/yuyin2x.png);
  background-size: cover;
  transform: translate(-50%, 0);
}

.btn-keyboard {
  left: 50%;
  width: 30px;
  height: 30px;
  background: url(./../img/keyboard.svg);
  background-size: cover;
}

.keyboard-action-wrapper {
  position: relative;
  display: none;
  padding: 17px 0;
  min-height: 106px;
  width: 100%;
  border-radius: 12px 12px 0px 0px;
  backdrop-filter: blur(0.23vh);
  /*background-image: url(./../img/medsci-logo.jpg);*/
  background-size: 20%, cover;
  background-repeat: no-repeat;
  background-position: 50% 81.5%, 0 0;
  z-index: 888;
}

.keyboard-action-wrapper img {
  width: 100%;
  display: block;
}

.button-wrapper {
  display: flex;
  align-items: center;
  padding: 0 32px;
  margin-bottom: 12px;
  margin-left: 50px;
  margin-top: -30px;
}

.btn-new-chat, .btn-help {
  width: 80px;
  height: 28px;
  border: 1px solid #FFFFFF;
  border-radius: 14px;
  font-size: 12px;
  color: #333333;
  display: flex;
  align-items: center;
  justify-content: center;
}

.btn-new-chat {
  background: #FFFFFF;
}

.btn-help {
  background-image: radial-gradient(circle at 24% 50%, #C0DAFF 0%, #23A0FD 55%);
}

.btn-new-chat {
  position: relative;
  padding-left: 30px;
  justify-content: flex-start;
}

.btn-new-chat::before {
  content: "";
  position: absolute;
  left: 10px;
  top: 50%;
  transform: translateY(-50%);
  width: 16px;
  height: 16px;
  background-image: url(./../img/new-duihua.png);
  background-repeat: no-repeat;
  background-position: center;
  background-size: contain;
  margin-right: 5px;
}

.btn-help {
  display: none;
  margin-left: 10px;
  position: relative;
  padding-left: 20px;
  justify-content: center;
  /*background-image: radial-gradient(circle at 24% 50%, #C0DAFF 0%, #23A0FD 55%);*/
}

.btn-help::before {
  content: "";
  position: absolute;
  left: 10px;
  top: 50%;
  transform: translateY(-50%);
  width: 16px;
  height: 16px;
  background-image: url(./../img/IP.png);
  background-repeat: no-repeat;
  background-position: center;
  background-size: contain;
}

.text-wrapper {
  display: flex;
  align-items: center;
  position: relative;
  padding: 0 20px 20px 20px;
  justify-content: right;
}

.btn-mic {
  width: 32px;
  height: 32px;
  background: url(./../img/yuyin.png);
  background-size: cover;
  margin-right: 10px;
}

.welcome {
  position: absolute;
  left: 12%;
  top: 10%;
  z-index: 100;
}

.welcome div:first-child {
  font-weight: 500;
  font-size: 24px;
  color: #122E44;
}

.welcome div:last-child {
  font-weight: 400;
  font-size: 14px;
  color: #1F3E57;
  margin-top: 8px;
}

.text-input {
  position: relative;
  width: 90%;
  height: 56px;
  background: #FFFFFF;
  border-radius: 8px;
  border: 1px solid transparent;
  background-image: linear-gradient(#fff, #fff), linear-gradient(to right, #1892FF, #4ED5F5);
  background-origin: border-box;
  background-clip: padding-box, border-box;
  padding: 0 36px 0 12px;
  outline: 0;
  text-align: left;
  font-size: 15px;
  color: #333333 !important;
  display: flex;
  align-items: center;
  line-height: 56px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.text-input::before {
  content: '请输入您的问题';
  color: #999999;
  position: absolute;
  left: 12px;
  top: 50%;
  transform: translateY(-50%);
  pointer-events: none;
}

.text-input:focus::before,
.text-input:not(:empty)::before {
  content: '';
}

.btn-send {
  position: absolute;
  top: 28px;
  right: 30px;
  transform: translateY(-50%);
  width: 24px;
  height: 24px;
  background: url(./../img/send.png) no-repeat center center;
  background-size: contain;
  border: none;
  cursor: pointer;
}

.video-area.bg::after {
  content: "";
  display: block;
  width: 100%;
  height: 12px;
  /* background: linear-gradient(
      180deg,
      rgba(255, 255, 255, 0) 0%,
      rgba(255, 255, 255, 0.8) 100%,
      rgba(255, 255, 255, 0.8) 100%
  ), */
linear-gradient(180deg, rgba(255, 255, 255, 0) 0%, #fff 100%, #fff 100%);
  position: absolute;
  bottom: 0;
}

.video-area > canvas {
  margin-left: 50%;
  transform: translateX(-50%);
}

.refs {
  border-radius: 6px;
  background: rgba(37, 85, 144, 0.10);
  margin-top: 8px;
  padding: 8px;
  display: none;
}

#refs-title {
  cursor: pointer;
  font-size: 15px;
  font-style: normal;
  font-weight: 400;
  line-height: 22px; /* 146.667% */
  color: rgba(1, 11, 50, 0.41);

}

#refs-toggle, #reason-title .arrow {
  display: inline-block;
  float: right;
  height: 22px;
}

#links-wrapper {
  margin-top: 4px;
  display: none;
  color: rgba(62, 110, 229, 0.93);
  font-size: 13px;
  font-style: normal;
  font-weight: 400;
  line-height: 22px; /* 169.231% */
  margin-bottom: -2px;
}

#links-wrapper a {
  color: rgba(62, 110, 229, 0.93);
  text-decoration: none;
  display: block;
  white-space: nowrap; /* 防止文本换行 */
  overflow: hidden; /* 隐藏溢出的内容 */
  text-overflow: ellipsis; /* 显示省略号 */
}

.hot {
  margin-left: 3px;
  padding: 4px;
  text-indent: 0;
  display: inline-block;
  font-size: 12px;
  line-height: 150%; /* 18px */
  width: 80px;
  height: 20px;
  flex-shrink: 0;
  border-radius: 3px;
  background: linear-gradient(93deg, #6F51ED 0%, #E776C5 140.08%);

}

.hot .text {
  float: right;
  margin-right: 2px;
  font-weight: 300;
  margin-top: -3px;
}

.reason {
  display: none;
  padding: 8px;
  border-radius: 6px;
  background: rgba(37, 85, 144, 0.10);
}

#reason-toggle {
  transform: rotate(180deg);
}

#reason-title {
  font-size: 15px;
  cursor: pointer;
  padding-bottom: 0px;
}

.reason pre {
  color: rgba(1, 11, 50, 0.41);
  font-size: 15px;
  line-height: 22px; /* 146.667% */
  margin-top: 7px;
}

.reasonning {
  position: relative;
  left: 3px;
  top: 2px;
  animation: rotate-animation 1s linear infinite;
}

@keyframes rotate-animation {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* Clickable text styling */
.clickable-text {
  cursor: pointer;
  /*padding: 8px 12px;*/
  margin: 5px 0;
  background-color: rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  transition: all 0.3s ease;
}

.clickable-text:active {
  transform: translateY(0);
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

/* 横屏布局样式 - 添加到文件末尾 */
.video-container {
  flex: 0 0 50%;
  position: relative;
  height: 100%;
  overflow: hidden;
}

.tcplayer.video-js,
.ivh-video-dimensions,
.tcp-skin {
  position: absolute !important;
  left: 0 !important;
  top: 50% !important;
  transform: translateY(-50%) !important;
  height: 100% !important;
  width: 100% !important;
  object-fit: cover;
}

.chat-user, .chat-ai {
  position: relative;
  top: auto;
  left: auto;
  right: auto;
  bottom: auto;
  margin: 10px 0;
  max-width: 80%;
}

/* .chat-user {
  margin-left: auto;
} */

.chat-ai {
  margin-right: auto;
}

.bg2 {
  position: absolute;
  top: 0;
  right: 0;
  width: 60%;
  height: 100vh;
  z-index: 1;
}

.action-wrapper {
  position: absolute;
  bottom: 0;
  width: 60%;
  right: 0;
  text-align: center;
}

/* 调整视频区域和聊天区域的响应式布局 */
@media (max-width: 768px) {
  .wrapper {
    flex-direction: column;
  }

  .video-container, .chat {
    flex: 0 0 100%;
  }

  .video-container {
    height: 50%;
  }

  .action-wrapper {
    width: 100%;
  }
}

.chat-container {
  position: absolute;
  top: 45%;
  right: 5%;
  transform: translateY(-50%);
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  max-width: 55%;
  min-width: 300px;
  /* background-color: #fff; */
  /* border: 1px solid #ddd; */
  border-radius: 10px;
  padding: 10px;
  z-index: 999;
  /* box-shadow: 0 0 10px rgba(0, 0, 0, 0.1); */
}


.text-line1 {
  width: 128px;
  height: 22px;
  font-family: PingFangSC-Medium;
  font-weight: 500;
  font-size: 16px;
  color: #333333;
}

.text-line2 {
  /*height: 20px;*/
  font-family: PingFangSC-Regular;
  font-weight: 400;
  font-size: 14px;
  color: #1892FF;
}

.bg-font {
  position: absolute;
  bottom: 106px;
  right: 0;
  width: 615px;
  z-index: 3;
  pointer-events: none;
}
