<script lang="ts" setup>
import FeeTypeModal from './usersModal.vue'
import { columns, searchFormSchema } from './user.data'
import { getFeeTypePage, deleteFeeType } from '@/api/medsciUsers'
import { useI18n } from '@/hooks/web/useI18n'
import { BasicTable, TableAction, useTable } from '@/components/Table'
import { useMessage } from '@/hooks/web/useMessage'
import { useModal } from '@/components/Modal'
import { IconEnum } from '@/enums/appEnum'

defineOptions({ name: 'FeeType' })

const { t } = useI18n()
const { createMessage } = useMessage()
const [registerModal, { openModal }] = useModal()

const [registerTable, { getForm, reload }] = useTable({
  title: '费用类型管理',
  api: getFeeTypePage,
  formConfig: { labelWidth: 120, schemas: searchFormSchema },
  columns,
  useSearchForm: true,
  showTableSetting: true,
  actionColumn: {
    width: 200,
    title: t('common.action'),
    dataIndex: 'action',
    fixed: 'right',
  }
})

function handleCreate() {
  openModal(true, { isUpdate: false })
}

function handleEdit(record: Recordable) {
  openModal(true, { record, isUpdate: true })
}

async function handleDelete(record: Recordable) {
  await deleteFeeType(record.id)
  createMessage.success(t('common.delSuccessText'))
  reload()
}

</script>
<template>
  <div>
    <BasicTable @register="registerTable">
      <template #toolbar>
        <a-button type="primary" :preIcon="IconEnum.ADD" @click="handleCreate">
          新增
        </a-button>
      </template>
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'action'">
          <TableAction
            :actions="[
              {
                icon: IconEnum.EDIT,
                label: '编辑',
                onClick: handleEdit.bind(null, record),
              },
              {
                icon: IconEnum.DELETE,
                danger: true,
                label: '删除',
                popConfirm: {
                  title: '是否确认删除该费用类型？',
                  placement: 'left',
                  confirm: handleDelete.bind(null, record),
                },
              },
            ]"
          />
        </template>
      </template>
    </BasicTable>
    <FeeTypeModal @register="registerModal" @success="reload()" />
  </div>
</template>