import type {BasicColumn, FormSchema} from '@/components/Table'
import {useRender} from '@/components/Table'
import {  getAppPage } from '@/api/medsciUsers'

export const columns: BasicColumn[] = [
  {
    title: 'ID',
    dataIndex: 'id',
    width: 80,
  },
  {
    title: '应用UUID',
    dataIndex: 'appUuid',
    width: 120,
  },
  {
    title: '语言',
    dataIndex: 'lang',
    width: 80,
  },
  {
    title: '显示名称',
    dataIndex: 'displayName',
    width: 120,
  },
  {
    title: '套餐标识',
    dataIndex: 'packageKey',
    width: 120,
  },
  {
    title: '套餐类型',
    dataIndex: 'packageType',
    width: 100,
  },
  {
    title: '周期类型',
    dataIndex: 'periodType',
    width: 80,
  },
  {
    title: '月数',
    dataIndex: 'monthNum',
    width: 60,
  },
  {
    title: '币种',
    dataIndex: 'coinType',
    width: 80,
  },
  {
    title: '原价',
    dataIndex: 'oldPrice',
    width: 80,
  },
  {
    title: '费用价格',
    dataIndex: 'feePrice',
    width: 80,
  },
  {
    title: '价格ID',
    dataIndex: 'priceId',
    width: 120,
  },
  {
    title: '数量',
    dataIndex: 'num',
    width: 60,
  },
  {
    title: '上线状态',
    dataIndex: 'online',
    width: 80,
    customRender: ({ text }) => {
      return text === 1 ? '上架' : '下架'
    }
  },
  {
    title: '价格类型',
    dataIndex: 'priceType',
    width: 100,
  },
  {
    title: '过期月数',
    dataIndex: 'expiredMonths',
    width: 80,
  },
  {
    title: '排序',
    dataIndex: 'orderNum',
    width: 60,
  },
  {
    title: '备注',
    dataIndex: 'remark',
    width: 150,
    ellipsis: true,
  },
]

export const searchFormSchema: FormSchema[] = [
  {
    label: '应用UUID',
    field: 'appUuid',
    component: 'Input',
    componentProps: {
      placeholder: '请输入应用UUID',
    },
    colProps: { span: 8 },
  },
  {
    label: '显示名称',
    field: 'displayName',
    component: 'Input',
    componentProps: {
      placeholder: '请输入显示名称',
    },
    colProps: { span: 8 },
  },
  {
    label: '套餐标识',
    field: 'packageKey',
    component: 'Input',
    componentProps: {
      placeholder: '请输入套餐标识',
    },
    colProps: { span: 8 },
  },
  {
    label: '套餐类型',
    field: 'packageType',
    component: 'Input',
    componentProps: {
      placeholder: '请输入套餐类型',
    },
    colProps: { span: 8 },
  },
  {
    label: '上线状态',
    field: 'online',
    component: 'Select',
    componentProps: {
      options: [
        { label: '上架', value: 1 },
        { label: '下架', value: 0 },
      ],
      placeholder: '请选择上线状态',
    },
    colProps: { span: 8 },
  },
  {
    label: '价格类型',
    field: 'priceType',
    component: 'Input',
    componentProps: {
      placeholder: '请输入价格类型',
    },
    colProps: { span: 8 },
  },
]

export const createFormSchema: FormSchema[] = [
  {
    label: '应用uuid',
    field: 'appUuid',
    component: 'ApiSelect',
    componentProps: {
      placeholder: '搜索应用名、英文名、简介、appUuid、 difyAppUuid',
      api: getAppPage,
      params: {
        pageNum: 1,
        pageSize: 10
      },
      resultField: 'list',
      labelField: 'appName',
      valueField: 'appUuid',
      searchField: 'keyword',
      showSearch: true,
      filterOption: false,
    },
    colProps: { span: 8 },
  },
  {
    label: '显示名称',
    field: 'displayName',
    component: 'Input',
    componentProps: {
      placeholder: '一般为 套餐类型',
    },
  },
  {
    label: '套餐标识',
    field: 'packageKey',
    component: 'Input',
    componentProps: {
      placeholder: '一般不填写，是应用的英文名。特殊的情况，请填写',
    },
  },
  {
    label: '套餐类型',
    field: 'packageType',
    component: 'Select',
    componentProps: {
      options: [
        { label: '周连续订阅', value: '周连续订阅' },
        { label: '连续包月', value: '连续包月' },
        { label: '连续包季', value: '连续包季' },
        { label: '连续包年', value: '连续包年' },
      ],
    },
    required: true,
  },
  {
    label: '周期类型',
    field: 'periodType',
    component: 'Select',
    componentProps: {
      options: [
        { label: '按月订阅', value: 'MONTH' },
        { label: '按天订阅', value: 'DAY' },
        { label: '按次付费', value: 'PER_USE' },
      ],
      placeholder: '请选择周期类型',
    },
    required: true,
    defaultValue: 'MONTH',
  },
  {
    label: '周期长度',
    field: 'monthNum',
    component: 'InputNumber',
    componentProps: {
      placeholder: '和 周期类型 配合使用',
      min: 1,
    },
    required: true,
    defaultValue: 1,
    helpMessage: '和 周期类型 配合使用',
  },
  {
    label: '原价',
    field: 'oldPrice',
    component: 'InputNumber',
    componentProps: {
      placeholder: '请输入原价',
      min: 0,
      precision: 2,
    },
  },
  {
    label: '费用价格',
    field: 'feePrice',
    component: 'InputNumber',
    componentProps: {
      placeholder: '请输入费用价格',
      min: 0,
      precision: 2,
    },
    required: true,
  },
  {
    label: '币种',
    field: 'coinType',
    component: 'Select',
    componentProps: {
      options: [
        { label: '人民币', value: '人民币' },
        { label: '美元', value: '美元' },
      ],
      placeholder: '请选择币种',
    },
    required: true,
    defaultValue: '人民币',
  },
  {
    label: '数量',
    field: 'num',
    component: 'InputNumber',
    componentProps: {
      placeholder: '限制使用次数，用于免费/按次付费',
      min: 1,
    },
    required: true,
    defaultValue: 1,
    helpMessage: '限制使用次数，用于免费/按次付费',
  },
  {
    label: '上线状态',
    field: 'online',
    component: 'RadioButtonGroup',
    componentProps: {
      options: [
        { label: '上架', value: 1 },
        { label: '下架', value: 0 },
      ],
    },
    required: true,
    defaultValue: 1,
  },
  {
    label: '过期月数',
    field: 'expiredMonths',
    component: 'InputNumber',
    componentProps: {
      placeholder: '请输入过期月数',
      min: 1,
    },
    required: true,
    defaultValue: 12,
  },
  {
    label: '排序',
    field: 'orderNum',
    component: 'InputNumber',
    componentProps: {
      placeholder: '请输入排序号',
      min: 1,
    },
    defaultValue: 1,
  },
  {
    label: '备注',
    field: 'remark',
    component: 'InputTextArea',
    componentProps: {
      placeholder: '请输入备注信息',
      rows: 3,
    },
  },
]

export const updateFormSchema: FormSchema[] = [
  {
    label: 'ID',
    field: 'id',
    component: 'Input',
    show: false,
  },
  ...createFormSchema,
]
