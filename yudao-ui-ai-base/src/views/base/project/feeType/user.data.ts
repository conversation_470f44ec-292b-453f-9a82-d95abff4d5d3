import type {BasicColumn, FormSchema} from '@/components/Table'
import {useRender} from '@/components/Table'

export const columns: BasicColumn[] = [
  {
    title: 'ID',
    dataIndex: 'id',
    width: 80,
  },
  {
    title: '应用UUID',
    dataIndex: 'appUuid',
    width: 120,
  },
  {
    title: '语言',
    dataIndex: 'lang',
    width: 80,
    customRender: ({ text }) => {
      return text === 'zh' ? '中文' : text === 'en' ? '英文' : text
    }
  },
  {
    title: '显示名称',
    dataIndex: 'displayName',
    width: 120,
  },
  {
    title: '套餐标识',
    dataIndex: 'packageKey',
    width: 120,
  },
  {
    title: '套餐类型',
    dataIndex: 'packageType',
    width: 100,
  },
  {
    title: '周期类型',
    dataIndex: 'periodType',
    width: 80,
  },
  {
    title: '月数',
    dataIndex: 'monthNum',
    width: 60,
  },
  {
    title: '原价',
    dataIndex: 'oldPrice',
    width: 80,
    customRender: ({ text }) => {
      return text ? `¥${text}` : '-'
    }
  },
  {
    title: '费用价格',
    dataIndex: 'feePrice',
    width: 80,
    customRender: ({ text }) => {
      return text ? `¥${text}` : '-'
    }
  },
  {
    title: '币种',
    dataIndex: 'coinType',
    width: 80,
  },
  {
    title: '价格ID',
    dataIndex: 'priceId',
    width: 120,
  },
  {
    title: '数量',
    dataIndex: 'num',
    width: 60,
  },
  {
    title: '上线状态',
    dataIndex: 'online',
    width: 80,
    customRender: ({ text }) => {
      return text === 1 ? '已上线' : '未上线'
    }
  },
  {
    title: '价格类型',
    dataIndex: 'priceType',
    width: 100,
  },
  {
    title: '过期月数',
    dataIndex: 'expiredMonths',
    width: 80,
  },
  {
    title: '排序',
    dataIndex: 'orderNum',
    width: 60,
  },
  {
    title: '备注',
    dataIndex: 'remark',
    width: 150,
    ellipsis: true,
  },
]

export const searchFormSchema: FormSchema[] = [
  {
    label: '应用UUID',
    field: 'appUuid',
    component: 'Input',
    componentProps: {
      placeholder: '请输入应用UUID',
    },
    colProps: { span: 8 },
  },
  {
    label: '显示名称',
    field: 'displayName',
    component: 'Input',
    componentProps: {
      placeholder: '请输入显示名称',
    },
    colProps: { span: 8 },
  },
  {
    label: '套餐标识',
    field: 'packageKey',
    component: 'Input',
    componentProps: {
      placeholder: '请输入套餐标识',
    },
    colProps: { span: 8 },
  },
  {
    label: '套餐类型',
    field: 'packageType',
    component: 'Input',
    componentProps: {
      placeholder: '请输入套餐类型',
    },
    colProps: { span: 8 },
  },
  {
    label: '上线状态',
    field: 'online',
    component: 'Select',
    componentProps: {
      options: [
        { label: '已上线', value: 1 },
        { label: '未上线', value: 0 },
      ],
      placeholder: '请选择上线状态',
    },
    colProps: { span: 8 },
  },
  {
    label: '价格类型',
    field: 'priceType',
    component: 'Input',
    componentProps: {
      placeholder: '请输入价格类型',
    },
    colProps: { span: 8 },
  },
]

export const createFormSchema: FormSchema[] = [
  {
    label: '应用UUID',
    field: 'appUuid',
    component: 'Input',
    componentProps: {
      placeholder: '请输入应用UUID',
    },
    required: true,
  },
  {
    label: '语言',
    field: 'lang',
    component: 'Select',
    componentProps: {
      options: [
        { label: '中文', value: 'zh' },
        { label: '英文', value: 'en' },
      ],
      placeholder: '请选择语言',
    },
    required: true,
    defaultValue: 'zh',
  },
  {
    label: '显示名称',
    field: 'displayName',
    component: 'Input',
    componentProps: {
      placeholder: '请输入显示名称',
    },
    required: true,
  },
  {
    label: '套餐标识',
    field: 'packageKey',
    component: 'Input',
    componentProps: {
      placeholder: '请输入套餐标识',
    },
    required: true,
  },
  {
    label: '套餐类型',
    field: 'packageType',
    component: 'Input',
    componentProps: {
      placeholder: '请输入套餐类型',
    },
    required: true,
  },
  {
    label: '周期类型',
    field: 'periodType',
    component: 'Select',
    componentProps: {
      options: [
        { label: '月', value: 'MONTH' },
        { label: '年', value: 'YEAR' },
        { label: '周', value: 'WEEK' },
        { label: '日', value: 'DAY' },
      ],
      placeholder: '请选择周期类型',
    },
    required: true,
    defaultValue: 'MONTH',
  },
  {
    label: '月数',
    field: 'monthNum',
    component: 'InputNumber',
    componentProps: {
      placeholder: '请输入月数',
      min: 1,
    },
    required: true,
    defaultValue: 1,
  },
  {
    label: '原价',
    field: 'oldPrice',
    component: 'InputNumber',
    componentProps: {
      placeholder: '请输入原价',
      min: 0,
      precision: 2,
    },
  },
  {
    label: '费用价格',
    field: 'feePrice',
    component: 'InputNumber',
    componentProps: {
      placeholder: '请输入费用价格',
      min: 0,
      precision: 2,
    },
    required: true,
  },
  {
    label: '币种',
    field: 'coinType',
    component: 'Select',
    componentProps: {
      options: [
        { label: '人民币', value: '人民币' },
        { label: '美元', value: '美元' },
        { label: '欧元', value: '欧元' },
      ],
      placeholder: '请选择币种',
    },
    required: true,
    defaultValue: '人民币',
  },
  {
    label: '价格ID',
    field: 'priceId',
    component: 'Input',
    componentProps: {
      placeholder: '请输入价格ID',
    },
  },
  {
    label: '数量',
    field: 'num',
    component: 'InputNumber',
    componentProps: {
      placeholder: '请输入数量',
      min: 1,
    },
    required: true,
    defaultValue: 1,
  },
  {
    label: '上线状态',
    field: 'online',
    component: 'RadioButtonGroup',
    componentProps: {
      options: [
        { label: '已上线', value: 1 },
        { label: '未上线', value: 0 },
      ],
    },
    required: true,
    defaultValue: 1,
  },
  {
    label: '价格类型',
    field: 'priceType',
    component: 'Select',
    componentProps: {
      options: [
        { label: '支付宝订阅', value: '支付宝订阅' },
        { label: '微信订阅', value: '微信订阅' },
        { label: '一次性支付', value: '一次性支付' },
      ],
      placeholder: '请选择价格类型',
    },
    required: true,
  },
  {
    label: '过期月数',
    field: 'expiredMonths',
    component: 'InputNumber',
    componentProps: {
      placeholder: '请输入过期月数',
      min: 1,
    },
    required: true,
    defaultValue: 12,
  },
  {
    label: '排序',
    field: 'orderNum',
    component: 'InputNumber',
    componentProps: {
      placeholder: '请输入排序号',
      min: 0,
    },
    defaultValue: 1,
  },
  {
    label: '备注',
    field: 'remark',
    component: 'InputTextArea',
    componentProps: {
      placeholder: '请输入备注信息',
      rows: 3,
    },
  },
]

export const updateFormSchema: FormSchema[] = [
  {
    label: 'ID',
    field: 'id',
    component: 'Input',
    show: false,
  },
  ...createFormSchema,
]
